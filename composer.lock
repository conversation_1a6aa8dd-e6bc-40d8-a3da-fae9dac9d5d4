{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "38b70908dc247657d2c434df7ec6cf42", "packages": [{"name": "aliyuncs/oss-sdk-php", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/572d0f8e099e8630ae7139ed3fdedb926c7a760f", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.6.0"}, "time": "2022-08-03T08:06:01+00:00"}, {"name": "brick/math", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-01-15T23:15:59+00:00"}, {"name": "cebe/php-openapi", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/cebe/php-openapi.git", "reference": "893ab104be1f5dfe5a39766703f583584e43c6e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/php-openapi/zipball/893ab104be1f5dfe5a39766703f583584e43c6e1", "reference": "893ab104be1f5dfe5a39766703f583584e43c6e1", "shasum": ""}, "require": {"ext-json": "*", "justinrainbow/json-schema": "^5.2 || ^6.0", "php": ">=7.1.0", "symfony/yaml": "^3.4 || ^4 || ^5 || ^6 || ^7.0"}, "conflict": {"symfony/yaml": "3.4.0 - 3.4.4 || 4.0.0 - 4.4.17 || 5.0.0 - 5.1.9 || 5.2.0"}, "require-dev": {"apis-guru/openapi-directory": "1.0.0", "cebe/indent": "*", "mermade/openapi3-examples": "1.0.0", "nexmo/api-specification": "1.0.0", "oai/openapi-specification": "3.0.3", "phpstan/phpstan": "^0.12.0 || ^1.9", "phpunit/phpunit": "^6.5 || ^7.5 || ^8.5 || ^9.4"}, "bin": ["bin/php-openapi"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"cebe\\openapi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://cebe.cc/", "role": "Creator"}], "description": "Read and write OpenAPI yaml/json files and make the content accessable in PHP objects.", "homepage": "https://github.com/cebe/php-openapi#readme", "keywords": ["openapi"], "support": {"issues": "https://github.com/cebe/php-openapi/issues", "source": "https://github.com/cebe/php-openapi"}, "time": "2025-05-07T08:44:12+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/semver", "version": "1.7.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/647490bbcaf7fc4891c58f47b825eb99d19c377a", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.7.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-12-03T15:47:16+00:00"}, {"name": "diff/diff", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/wmde/Diff.git", "reference": "7aa40f2260ad44be0978821742b9aa8c0af508b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wmde/Diff/zipball/7aa40f2260ad44be0978821742b9aa8c0af508b3", "reference": "7aa40f2260ad44be0978821742b9aa8c0af508b3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "require-dev": {"ockcyp/covers-validator": "~1.0", "phpunit/phpunit": "~8.5.0", "squizlabs/php_codesniffer": "~3.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"files": ["Diff.php"], "psr-4": {"Diff\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "jero<PERSON><EMAIL>", "homepage": "https://www.entropywins.wtf", "role": "Developer"}], "description": "Small standalone library for representing differences between data structures, computing such differences, and applying them as patches", "homepage": "https://github.com/wmde/Diff", "keywords": ["diff", "diffing", "diffop", "patch", "patching", "wikidata"], "support": {"irc": "irc://irc.freenode.net/wikimedia-de-tech", "issues": "https://github.com/wmde/Diff/issues", "source": "https://github.com/wmde/Diff/tree/3.3.1"}, "time": "2022-10-06T08:57:22+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/4f2d4f2836e7ec4e7a8625e75c6aa916004db931", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.2"}, "time": "2023-09-27T20:04:15+00:00"}, {"name": "doctrine/lexer", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-12-14T08:49:07+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2023-08-10T19:36:49+00:00"}, {"name": "ergebnis/classy", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/ergebnis/classy.git", "reference": "731295995e6b7e9cc634fed27eaec5730596c3f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ergebnis/classy/zipball/731295995e6b7e9cc634fed27eaec5730596c3f3", "reference": "731295995e6b7e9cc634fed27eaec5730596c3f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-tokenizer": "*", "php": "^8.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.28.3", "ergebnis/license": "^2.1.0", "ergebnis/php-cs-fixer-config": "^4.11.0", "fakerphp/faker": "^1.20.0", "infection/infection": "~0.26.6", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "~0.18.3", "vimeo/psalm": "^4.29.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Ergebnis\\Classy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a finder for classy constructs (classes, enums, interfaces, and traits).", "homepage": "https://github.com/ergebnis/classy", "keywords": ["classes", "classy", "constructs", "finder", "interfaces", "traits"], "support": {"issues": "https://github.com/ergebnis/classy/issues", "source": "https://github.com/ergebnis/classy"}, "time": "2022-11-28T15:42:41+00:00"}, {"name": "firebase/php-jwt", "version": "v6.10.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "500501c2ce893c824c801da135d02661199f60c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/500501c2ce893c824c801da135d02661199f60c5", "reference": "500501c2ce893c824c801da135d02661199f60c5", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.10.1"}, "time": "2024-05-18T18:05:11+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "1110f66a6530a40fe7aea0378fe608ee2b2248f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/1110f66a6530a40fe7aea0378fe608ee2b2248f9", "reference": "1110f66a6530a40fe7aea0378fe608ee2b2248f9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-08-27T10:20:53+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/111166291a0f8130081195ac4556a5587d7f1b5d", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-08-03T15:11:55+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-08-27T10:13:57+00:00"}, {"name": "hashids/hashids", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/vinkla/hashids.git", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vinkla/hashids/zipball/8cab111f78e0bd9c76953b082919fc9e251761be", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0 || ^9.4", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Hashids\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generate short, unique, non-sequential ids (like YouTube and Bitly) from numbers", "homepage": "https://hashids.org/php", "keywords": ["bitly", "decode", "encode", "hash", "hashid", "hashids", "ids", "obfuscate", "youtube"], "support": {"issues": "https://github.com/vinkla/hashids/issues", "source": "https://github.com/vinkla/hashids/tree/4.1.0"}, "time": "2020-11-26T19:24:33+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "jcchavezs/zipkin-opentracing", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/jcchavezs/zipkin-php-opentracing.git", "reference": "bb8f1c5c3643594c0328c7df98e49fef86c85710"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jcchavezs/zipkin-php-opentracing/zipball/bb8f1c5c3643594c0328c7df98e49fef86c85710", "reference": "bb8f1c5c3643594c0328c7df98e49fef86c85710", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"opentracing/opentracing": "^1.0.1", "openzipkin/zipkin": "^3.0.0", "php": ">=7.4 || ^8.0"}, "provide": {"opentracing/opentracing": "1.0.0"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"ZipkinOpenTracing\\": "./src/ZipkinOpenTracing/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin bridge with OpenTracing", "support": {"issues": "https://github.com/jcchavezs/zipkin-php-opentracing/issues", "source": "https://github.com/jcchavezs/zipkin-php-opentracing/tree/2.0.3"}, "funding": [{"url": "https://www.paypal.me/jcchavezs", "type": "paypal"}], "time": "2023-06-08T11:40:46+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.2", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/ce1fd2d47799bb60668643bc6220f6278a4c1d02", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.2"}, "time": "2025-06-03T18:27:04+00:00"}, {"name": "k8s/api", "version": "v1.25.15", "source": {"type": "git", "url": "https://github.com/k8s-client/api.git", "reference": "0f00d880580ef25993e19d4d98eec41ba58a3028"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/k8s-client/api/zipball/0f00d880580ef25993e19d4d98eec41ba58a3028", "reference": "0f00d880580ef25993e19d4d98eec41ba58a3028", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"k8s/core": "^1.0", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "k8s/api-generator": "~0.2", "phpstan/phpstan": "^0.12"}, "type": "library", "autoload": {"psr-4": {"K8s\\Api\\": "src/K8s/Api"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Kubernetes API models and services generated from the OpenAPI spec.", "support": {"issues": "https://github.com/k8s-client/api/issues", "source": "https://github.com/k8s-client/api/tree/v1.25.15"}, "time": "2023-10-19T00:47:31+00:00"}, {"name": "k8s/client", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/k8s-client/client.git", "reference": "2e79d2c3a732c8264fd6183c15d0ff77c794eb9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/k8s-client/client/zipball/2e79d2c3a732c8264fd6183c15d0ff77c794eb9c", "reference": "2e79d2c3a732c8264fd6183c15d0ff77c794eb9c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/annotations": "^1.0", "ext-json": "*", "k8s/api": "*", "k8s/core": "^1.3", "klkvsk/json-decode-stream": "^1.0", "php": ">=7.2", "php-http/discovery": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0", "symfony/yaml": ">=3.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "k8s/http-symfony": "^1.0", "k8s/ws-ratchet": "^1.0", "mockery/mockery": "~1.3.0", "php-http/httplug": "^2.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5"}, "suggest": {"k8s/http-guzzle": "Guzzle based HttpClient for k8s client auto-configuration.", "k8s/http-symfony": "Symfony based HttpClient for k8s client auto-configuration.", "k8s/ws-ratchet": "Ratchet based websocket adapter for websocket based API requests.", "k8s/ws-swoole": "Swoole based websocket adapter for websocket based API requests.", "symfony/cache": "For a PSR-16 implementation to cache Kubernetes model metadata information."}, "type": "library", "autoload": {"psr-4": {"K8s\\Client\\": "src/K8s/Client"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Kubernetes client for PHP.", "keywords": ["k8s", "kubernetes"], "support": {"issues": "https://github.com/k8s-client/client/issues", "source": "https://github.com/k8s-client/client/tree/1.7.0"}, "time": "2021-06-20T17:01:09+00:00"}, {"name": "k8s/core", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/k8s-client/core.git", "reference": "1afa5d377e25c0543e8da451bc16cbd1b3ced6b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/k8s-client/core/zipball/1afa5d377e25c0543e8da451bc16cbd1b3ced6b6", "reference": "1afa5d377e25c0543e8da451bc16cbd1b3ced6b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/collections": "^1.5", "php": ">=7.2", "psr/http-client": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.12"}, "type": "library", "autoload": {"psr-4": {"K8s\\Core\\": "src/K8s/Core"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A set of needed interfaces, classes and exceptions for the k8s-client, k8s-api, and the websocket adapters.", "support": {"issues": "https://github.com/k8s-client/core/issues", "source": "https://github.com/k8s-client/core/tree/1.3.0"}, "time": "2021-03-07T20:00:55+00:00"}, {"name": "k8s/http-guzzle", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/k8s-client/http-guzzle.git", "reference": "5e0523dd8fa64ba055f118af28623e0f62152e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/k8s-client/http-guzzle/zipball/5e0523dd8fa64ba055f118af28623e0f62152e25", "reference": "5e0523dd8fa64ba055f118af28623e0f62152e25", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^7.0", "k8s/core": "^1.3", "php": ">=7.2", "psr/http-client": "^1.0"}, "require-dev": {"ext-mbstring": "*", "friendsofphp/php-cs-fixer": "^2.0", "mockery/mockery": "~1.3.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.0"}, "type": "library", "autoload": {"psr-4": {"K8s\\HttpGuzzle\\": "src/K8s/HttpGuzzle"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle based HttpClient factory for k8s/client", "keywords": ["k8s", "kubernetes"], "support": {"issues": "https://github.com/k8s-client/http-guzzle/issues", "source": "https://github.com/k8s-client/http-guzzle/tree/1.0.0"}, "time": "2021-03-14T19:27:07+00:00"}, {"name": "klkvsk/json-decode-stream", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/klkvsk/json-decode-stream.git", "reference": "831b5310b42b51705a2d6ae5353bba7aaa302358"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/klkvsk/json-decode-stream/zipball/831b5310b42b51705a2d6ae5353bba7aaa302358", "reference": "831b5310b42b51705a2d6ae5353bba7aaa302358", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"nyholm/psr7": "^1.3", "phpunit/phpunit": "*", "psr/http-message": "^1.0", "vimeo/psalm": "*"}, "type": "library", "autoload": {"psr-4": {"JsonDecodeStream\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "JSON streaming reader", "homepage": "https://github.com/klkvsk/json-decode-stream", "keywords": ["decode", "json", "parse", "stream"], "support": {"issues": "https://github.com/klkvsk/json-decode-stream/issues", "source": "https://github.com/klkvsk/json-decode-stream/tree/v1.0.3"}, "time": "2021-06-29T23:00:36+00:00"}, {"name": "knplabs/github-api", "version": "v3.12.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/php-github-api.git", "reference": "b50fc1f40bb5ac50957d32c5732fcde9167ac30a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/php-github-api/zipball/b50fc1f40bb5ac50957d32c5732fcde9167ac30a", "reference": "b50fc1f40bb5ac50957d32c5732fcde9167ac30a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.2.5 || ^8.0", "php-http/cache-plugin": "^1.7.1", "php-http/client-common": "^2.3", "php-http/discovery": "^1.12", "php-http/httplug": "^2.2", "php-http/multipart-stream-builder": "^1.1.2", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0|^2.0", "symfony/deprecation-contracts": "^2.2|^3.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"guzzlehttp/guzzle": "^7.2", "guzzlehttp/psr7": "^1.7", "http-interop/http-factory-guzzle": "^1.0", "php-http/mock-client": "^1.4.1", "phpstan/extension-installer": "^1.0.5", "phpstan/phpstan": "^0.12.57", "phpstan/phpstan-deprecation-rules": "^0.12.5", "phpunit/phpunit": "^8.5 || ^9.4", "symfony/cache": "^5.1.8", "symfony/phpunit-bridge": "^5.2"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.20.x-dev", "dev-master": "3.11.x-dev"}}, "autoload": {"psr-4": {"Github\\": "lib/Github/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://ornicar.github.com"}], "description": "GitHub API v3 client", "homepage": "https://github.com/KnpLabs/php-github-api", "keywords": ["api", "gh", "gist", "github"], "support": {"issues": "https://github.com/KnpLabs/php-github-api/issues", "source": "https://github.com/KnpLabs/php-github-api/tree/v3.12.0"}, "funding": [{"url": "https://github.com/acrobat", "type": "github"}], "time": "2023-09-30T16:42:04+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/flysystem-cached-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/d1925efb2207ac4be3ad0c40b8277175f99ffaff", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "support": {"issues": "https://github.com/thephpleague/flysystem-cached-adapter/issues", "source": "https://github.com/thephpleague/flysystem-cached-adapter/tree/master"}, "time": "2020-07-25T15:56:04+00:00"}, {"name": "league/mime-type-detection", "version": "1.14.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/b6a5854368533df0295c5761a0253656a2e52d9e", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.14.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2023-10-17T14:13:20+00:00"}, {"name": "m4tthumphrey/php-gitlab-api", "version": "11.12.0", "source": {"type": "git", "url": "https://github.com/GitLabPHP/Client.git", "reference": "b0d70a6142c52407a226d940ef2d0ae96e65c7e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GitLabPHP/Client/zipball/b0d70a6142c52407a226d940ef2d0ae96e65c7e7", "reference": "b0d70a6142c52407a226d940ef2d0ae96e65c7e7", "shasum": ""}, "require": {"ext-json": "*", "ext-xml": "*", "php": "^7.4.15 || ^8.0.2", "php-http/cache-plugin": "^1.8", "php-http/client-common": "^2.7", "php-http/discovery": "^1.19", "php-http/httplug": "^2.4", "php-http/multipart-stream-builder": "^1.3", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.1 || ^2.0", "symfony/options-resolver": "^4.4 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.26"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "guzzlehttp/guzzle": "^7.8", "http-interop/http-factory-guzzle": "^1.2"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"Gitlab\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/fbourigault"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/m4tthumphrey"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/m1guelpf"}], "description": "GitLab API v4 client for PHP", "keywords": ["api", "gitlab"], "support": {"issues": "https://github.com/GitLabPHP/Client/issues", "source": "https://github.com/GitLabPHP/Client/tree/11.12.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}], "time": "2023-10-08T14:28:30+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "nesbot/carbon", "version": "2.71.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "98276233188583f2ff845a0f992a235472d9466a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/98276233188583f2ff845a0f992a235472d9466a", "reference": "98276233188583f2ff845a0f992a235472d9466a", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2023-09-25T11:31:05+00:00"}, {"name": "nette/php-generator", "version": "v4.1.1", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "7f53567906c577429ac612c4ed6bae5425c9eed2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/7f53567906c577429ac612c4ed6bae5425c9eed2", "reference": "7f53567906c577429ac612c4ed6bae5425c9eed2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.3"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.15", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.3 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.1"}, "time": "2023-10-17T08:13:39+00:00"}, {"name": "nette/utils", "version": "v4.0.5", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.5"}, "time": "2024-08-07T15:39:19+00:00"}, {"name": "open-smf/connection-pool", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/open-smf/connection-pool.git", "reference": "b7b06d26385540bc528f1a8567c8067b36715368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/open-smf/connection-pool/zipball/b7b06d26385540bc528f1a8567c8067b36715368", "reference": "b7b06d26385540bc528f1a8567c8067b36715368", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-swoole": ">=4.2.9", "php": ">=8.0.0"}, "require-dev": {"swoole/ide-helper": "@dev"}, "suggest": {"ext-redis": "A PHP extension for Redis."}, "type": "library", "autoload": {"psr-4": {"Smf\\ConnectionPool\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A common connection pool based on Swoole is usually used as the database connection pool.", "homepage": "https://github.com/open-smf/connection-pool", "keywords": ["connection-pool", "database-connection-pool", "swoole"], "support": {"issues": "https://github.com/open-smf/connection-pool/issues", "source": "https://github.com/open-smf/connection-pool"}, "time": "2023-08-25T10:17:56+00:00"}, {"name": "opentracing/opentracing", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/opentracing/opentracing-php.git", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentracing/opentracing-php/zipball/cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "~0.12", "phpunit/phpunit": "^7.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"files": ["src/OpenTracing/Tags.php", "src/OpenTracing/Formats.php"], "psr-4": {"OpenTracing\\": "src/OpenTracing/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "support": {"issues": "https://github.com/opentracing/opentracing-php/issues", "source": "https://github.com/opentracing/opentracing-php/tree/1.0.2"}, "time": "2022-01-27T19:59:21+00:00"}, {"name": "openzipkin/zipkin", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/openzipkin/zipkin-php.git", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openzipkin/zipkin-php/zipball/e2809f8b6775796d2116b3ca73576a1734296ff6", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-curl": "*", "php": "^7.4 || ^8.0", "psr/http-message": "~1.0 || ~2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"ext-mysqli": "*", "jcchavezs/httptest": "~0.2", "middlewares/fast-route": "^2.0", "middlewares/request-handler": "^2.0", "nyholm/psr7": "^1.4", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "~9", "psr/http-client": "^1.0", "psr/http-server-middleware": "^1.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-mysqli": "Allows to use mysqli instrumentation.", "psr/http-client": "Allows to instrument HTTP clients following PSR18.", "psr/http-server-middleware": "Allows to instrument HTTP servers via middlewares following PSR15."}, "type": "library", "autoload": {"files": ["./src/Zipkin/Propagation/Id.php", "./src/Zipkin/Timestamp.php", "./src/Zipkin/Kind.php", "./src/Zipkin/Tags.php", "./src/Zipkin/Annotations.php", "./src/Zipkin/SpanName.php"], "psr-4": {"Zipkin\\": "./src/<PERSON>ip<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin instrumentation for PHP", "homepage": "https://github.com/openzipkin/zipkin-php", "keywords": ["distributed-tracing", "openzipkin", "tracing", "zipkin"], "support": {"issues": "https://github.com/openzipkin/zipkin-php/issues", "source": "https://github.com/openzipkin/zipkin-php/tree/3.2.0"}, "time": "2023-09-28T20:54:04+00:00"}, {"name": "php-http/cache-plugin", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/php-http/cache-plugin.git", "reference": "b3e6c25d89ee5e4ac82115ed23b21ba87986d614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/cache-plugin/zipball/b3e6c25d89ee5e4ac82115ed23b21ba87986d614", "reference": "b3e6c25d89ee5e4ac82115ed23b21ba87986d614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/client-common": "^1.9 || ^2.0", "php-http/message-factory": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/options-resolver": "^2.6 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\Plugin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PSR-6 Cache plugin for HTTPlug", "homepage": "http://httplug.io", "keywords": ["cache", "http", "httplug", "plugin"], "support": {"issues": "https://github.com/php-http/cache-plugin/issues", "source": "https://github.com/php-http/cache-plugin/tree/1.8.1"}, "time": "2023-11-21T08:52:56+00:00"}, {"name": "php-http/client-common", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/0cfe9858ab9d3b213041b947c881d5b19ceeca46", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.2"}, "time": "2024-09-24T06:21:48+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.1"}, "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/10086e6de6f53489cca5ecc45b6f468604d3460e", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.4.2"}, "time": "2024-09-04T13:22:54+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.45", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "28d8f438a0064c9de80857e3270d071495544640"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/28d8f438a0064c9de80857e3270d071495544640", "reference": "28d8f438a0064c9de80857e3270d071495544640", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.45"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2023-09-15T20:55:47+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-27T19:12:24+00:00"}, {"name": "ramsey/uuid", "version": "4.7.4", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "60a4c63ab724854332900504274f6150ff26d286"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/60a4c63ab724854332900504274f6150ff26d286", "reference": "60a4c63ab724854332900504274f6150ff26d286", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.4"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2023-04-15T23:01:58+00:00"}, {"name": "spatie/macroable", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/spatie/macroable.git", "reference": "ec2c320f932e730607aff8052c44183cf3ecb072"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/macroable/zipball/ec2c320f932e730607aff8052c44183cf3ecb072", "reference": "ec2c320f932e730607aff8052c44183cf3ecb072", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Macroable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A trait to dynamically add methods to a class", "homepage": "https://github.com/spatie/macroable", "keywords": ["macroable", "spatie"], "support": {"issues": "https://github.com/spatie/macroable/issues", "source": "https://github.com/spatie/macroable/tree/2.0.0"}, "time": "2021-03-26T22:39:02+00:00"}, {"name": "spatie/url", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/spatie/url.git", "reference": "70468105958905d9a8566409212715e9754ca242"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/url/zipball/70468105958905d9a8566409212715e9754ca242", "reference": "70468105958905d9a8566409212715e9754ca242", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.0", "psr/http-message": "^1.0 || ^2.0", "spatie/macroable": "^2.0"}, "require-dev": {"pestphp/pest": "^1.21"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Url\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Parse, build and manipulate URL's", "homepage": "https://github.com/spatie/url", "keywords": ["spatie", "url"], "support": {"issues": "https://github.com/spatie/url/issues", "source": "https://github.com/spatie/url/tree/2.2.1"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2023-04-27T11:07:22+00:00"}, {"name": "stechstudio/backoff", "version": "1.2", "source": {"type": "git", "url": "https://github.com/stechstudio/backoff.git", "reference": "816e46107a6be2e1072ba0ff2cb26034872dfa49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stechstudio/backoff/zipball/816e46107a6be2e1072ba0ff2cb26034872dfa49", "reference": "816e46107a6be2e1072ba0ff2cb26034872dfa49", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"phpunit/phpunit": "5.5.*"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"STS\\Backoff\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP library providing retry functionality with multiple backoff strategies and jitter support", "support": {"issues": "https://github.com/stechstudio/backoff/issues", "source": "https://github.com/stechstudio/backoff/tree/1.2"}, "time": "2020-12-26T14:57:10+00:00"}, {"name": "swoole/ide-helper", "version": "4.8.13", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "d100c446b2e3d56430cbcab5dc3fa20a9f35c4ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/d100c446b2e3d56430cbcab5dc3fa20a9f35c4ef", "reference": "d100c446b2e3d56430cbcab5dc3fa20a9f35c4ef", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/4.8.13"}, "time": "2023-03-20T06:46:24+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.25", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-31T13:04:02+00:00"}, {"name": "symfony/finder", "version": "v5.4.27", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ff4bce3c33451e7ec778070e45bd23f74214cd5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ff4bce3c33451e7ec778070e45bd23f74214cd5d", "reference": "ff4bce3c33451e7ec778070e45bd23f74214cd5d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.27"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-31T08:02:31+00:00"}, {"name": "symfony/options-resolver", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "0a62a9f2504a8dd27083f89d21894ceb01cc59db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/0a62a9f2504a8dd27083f89d21894ceb01cc59db", "reference": "0a62a9f2504a8dd27083f89d21894ceb01cc59db", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "42292d99c55abe617799667f454222c54c60e229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/42292d99c55abe617799667f454222c54c60e229", "reference": "42292d99c55abe617799667f454222c54c60e229", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-28T09:04:16+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "7581cd600fa9fd681b797d00b02f068e2f13263b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/7581cd600fa9fd681b797d00b02f068e2f13263b", "reference": "7581cd600fa9fd681b797d00b02f068e2f13263b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/process", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "2114fd60f26a296cc403a7939ab91478475a33d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/2114fd60f26a296cc403a7939ab91478475a33d4", "reference": "2114fd60f26a296cc403a7939ab91478475a33d4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/acbfbb274e730e5a0236f619b6168d9dedb3e282", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/workflow", "version": "v5.4.28", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "824480ed211164a31fc42e6cea912273699a52a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/824480ed211164a31fc42e6cea912273699a52a6", "reference": "824480ed211164a31fc42e6cea912273699a52a6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/event-dispatcher": "<4.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/validator": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools for managing a workflow or finite state machine", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "support": {"source": "https://github.com/symfony/workflow/tree/v5.4.28"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-08-01T08:27:53+00:00"}, {"name": "symfony/yaml", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "deec3a812a0305a50db8ae689b183f43d915c884"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/deec3a812a0305a50db8ae689b183f43d915c884", "reference": "deec3a812a0305a50db8ae689b183f43d915c884", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-11T11:50:03+00:00"}, {"name": "topthink/framework", "version": "8.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "3610b6fb01c3373c41c70db872e792420e77d059"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/3610b6fb01c3373c41c70db872e792420e77d059", "reference": "3610b6fb01c3373c41c70db872e792420e77d059", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=8.0.0", "psr/http-message": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "topthink/think-container": "^3.0", "topthink/think-helper": "^3.1", "topthink/think-orm": "^3.0|^4.0", "topthink/think-validate": "^3.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^9.5"}, "default-branch": true, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/8.x"}, "time": "2025-01-09T05:07:00+00:00"}, {"name": "topthink/gitlib", "version": "dev-master", "source": {"type": "git", "url": "https://git.topthink.com/topteam/gitlib.git", "reference": "7d75fbac2f283523cfbf985ab5ed1bed3caf87f2"}, "require": {"ext-pcre": "*", "php": "^8.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/process": "^4.0 || ^5.0 || ^6.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"topthink\\git\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for accessing git", "time": "2025-02-28T06:03:01+00:00"}, {"name": "topthink/think-agent", "version": "dev-master", "source": {"type": "git", "url": "https://git.topthink.com/topteam/think-agent", "reference": "adf69f064294a0e2bc5e97dbec61918286a5e0de"}, "require": {"cebe/php-openapi": "^1.7", "php": ">=8.0", "topthink/framework": ">=6.0", "topthink/think-ai": "*", "topthink/think-helper": "^3.1"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"think\\agent\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2025-08-14T11:40:07+00:00"}, {"name": "topthink/think-ai", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/top-think/think-ai.git", "reference": "a2476473c6c91bc2c90d50bbb2d1e482c3f88df2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-ai/zipball/a2476473c6c91bc2c90d50bbb2d1e482c3f88df2", "reference": "a2476473c6c91bc2c90d50bbb2d1e482c3f88df2", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": ">=8.0", "topthink/think-helper": "^3.1"}, "type": "library", "autoload": {"psr-4": {"think\\ai\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-ai/issues", "source": "https://github.com/top-think/think-ai/tree/v1.0.1"}, "time": "2025-08-10T08:39:10+00:00"}, {"name": "topthink/think-annotation", "version": "2.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-annotation.git", "reference": "293dae6f2d7bdb6fed1a89babe176f6059738ccf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-annotation/zipball/293dae6f2d7bdb6fed1a89babe176f6059738ccf", "reference": "293dae6f2d7bdb6fed1a89babe176f6059738ccf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ergebnis/classy": "^1.4", "php": "^8.0", "topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"topthink/think-ide-helper": "^1.0"}, "default-branch": true, "type": "library", "extra": {"think": {"services": ["think\\annotation\\Service"], "config": {"annotation": "src/config.php"}}}, "autoload": {"psr-4": {"think\\annotation\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Annotation For ThinkPHP6", "support": {"issues": "https://github.com/top-think/think-annotation/issues", "source": "https://github.com/top-think/think-annotation/tree/v2.0.4"}, "time": "2023-07-01T10:56:40+00:00"}, {"name": "topthink/think-container", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/top-think/think-container.git", "reference": "b2df244be1e7399ad4c8be1ccc40ed57868f730a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-container/zipball/b2df244be1e7399ad4c8be1ccc40ed57868f730a", "reference": "b2df244be1e7399ad4c8be1ccc40ed57868f730a", "shasum": ""}, "require": {"php": ">=8.0", "psr/container": "^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "support": {"issues": "https://github.com/top-think/think-container/issues", "source": "https://github.com/top-think/think-container/tree/v3.0.2"}, "time": "2025-04-07T03:21:51+00:00"}, {"name": "topthink/think-filesystem", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/top-think/think-filesystem.git", "reference": "29f19f140a9267c717fecd7ccb22c84c2d72382e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-filesystem/zipball/29f19f140a9267c717fecd7ccb22c84c2d72382e", "reference": "29f19f140a9267c717fecd7ccb22c84c2d72382e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "^1.1.4", "league/flysystem-cached-adapter": "^1.0", "php": ">=7.2.5", "topthink/framework": "^6.1|^8.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^8.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6.1 Filesystem Package", "support": {"issues": "https://github.com/top-think/think-filesystem/issues", "source": "https://github.com/top-think/think-filesystem/tree/v1.0.3"}, "time": "2023-02-08T01:25:15+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/769acbe50a4274327162f9c68ec2e89a38eb2aff", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.6"}, "time": "2021-12-15T04:27:55+00:00"}, {"name": "topthink/think-migration", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/top-think/think-migration.git", "reference": "22c44058e1454f3af1d346e7f6524fbe654de7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-migration/zipball/22c44058e1454f3af1d346e7f6524fbe654de7fb", "reference": "22c44058e1454f3af1d346e7f6524fbe654de7fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2", "topthink/framework": "^6.0 || ^8.0", "topthink/think-helper": "^3.0.3"}, "require-dev": {"composer/composer": "^2.5.8", "fzaninotto/faker": "^1.8", "robmorgan/phinx": "^0.13.4"}, "suggest": {"fzaninotto/faker": "Required to use the factory builder (^1.8)."}, "type": "library", "extra": {"think": {"services": ["think\\migration\\Service"]}}, "autoload": {"psr-4": {"Phinx\\": "phinx", "think\\migration\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-migration/issues", "source": "https://github.com/top-think/think-migration/tree/v3.1.1"}, "time": "2023-09-14T05:51:31+00:00"}, {"name": "topthink/think-orm", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "47804c47c7a8b2bccfa694448bc769d3edac0290"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/47804c47c7a8b2bccfa694448bc769d3edac0290", "reference": "47804c47c7a8b2bccfa694448bc769d3edac0290", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=8.0.0", "psr/log": ">=1.0", "psr/simple-cache": ">=1.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.6|^10"}, "suggest": {"ext-mongodb": "provide mongodb support"}, "default-branch": true, "type": "library", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the PHP Database&ORM Framework", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/3.0"}, "time": "2024-12-25T09:23:45+00:00"}, {"name": "topthink/think-queue", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "654812b47dd7c708c4443deed27f212f8382e8da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/654812b47dd7c708c4443deed27f212f8382e8da", "reference": "654812b47dd7c708c4443deed27f212f8382e8da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "nesbot/carbon": "^2.16", "symfony/process": ">=4.2", "topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.2", "phpunit/phpunit": "^6.2", "topthink/think-migration": "^3.0"}, "default-branch": true, "type": "library", "extra": {"think": {"services": ["think\\queue\\Service"], "config": {"queue": "src/config.php"}}}, "autoload": {"files": ["src/common.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Queue Package", "support": {"issues": "https://github.com/top-think/think-queue/issues", "source": "https://github.com/top-think/think-queue/tree/v3.0.9"}, "time": "2023-07-03T05:42:01+00:00"}, {"name": "topthink/think-swoole", "version": "4.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-swoole.git", "reference": "a867801855a1cff7e6c6e755bad7adbd77304a03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-swoole/zipball/a867801855a1cff7e6c6e755bad7adbd77304a03", "reference": "a867801855a1cff7e6c6e755bad7adbd77304a03", "shasum": ""}, "require": {"ext-json": "*", "ext-swoole": ">=4.6", "nette/php-generator": "^4.0", "open-smf/connection-pool": ">=1.0", "php": "^8.0", "stechstudio/backoff": "^1.2", "swoole/ide-helper": "^4.3", "symfony/finder": "^4.3.2|^5.1", "topthink/framework": "^6.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/var-dumper": "^4.3|^5.1", "topthink/think-queue": "^3.0", "topthink/think-tracing": "^1.0"}, "default-branch": true, "type": "library", "extra": {"think": {"services": ["think\\swoole\\Service"], "config": {"swoole": "src/config/swoole.php"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"think\\swoole\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "Swoole extend for thinkphp", "support": {"issues": "https://github.com/top-think/think-swoole/issues", "source": "https://github.com/top-think/think-swoole/tree/4.0"}, "time": "2024-11-26T04:18:47+00:00"}, {"name": "topthink/think-tracing", "version": "v1.0.12", "source": {"type": "git", "url": "https://github.com/top-think/think-tracing.git", "reference": "3c7dc888cd6eaebd0a432d0449232b2758e94a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-tracing/zipball/3c7dc888cd6eaebd0a432d0449232b2758e94a90", "reference": "3c7dc888cd6eaebd0a432d0449232b2758e94a90", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "opentracing/opentracing": "^1.0", "php": ">=7.1.0", "topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"jcchavezs/zipkin-opentracing": "^1.0 || ^2.0", "jonahgeorge/jaeger-client-php": "^1.0"}, "suggest": {"jcchavezs/zipkin-opentracing": "zipkin", "jonahgeorge/jaeger-client-php": "jaeger"}, "type": "library", "extra": {"think": {"services": ["think\\tracing\\Service"], "config": {"tracing": "config/tracing.php"}}}, "autoload": {"psr-4": {"think\\tracing\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Distributed tracing for ThinkPHP made easy", "support": {"issues": "https://github.com/top-think/think-tracing/issues", "source": "https://github.com/top-think/think-tracing/tree/v1.0.12"}, "time": "2023-12-23T03:41:39+00:00"}, {"name": "topthink/think-validate", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/top-think/think-validate.git", "reference": "2729f938952a01e9d0e1a17d742b07fcf960ae31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-validate/zipball/2729f938952a01e9d0e1a17d742b07fcf960ae31", "reference": "2729f938952a01e9d0e1a17d742b07fcf960ae31", "shasum": ""}, "require": {"php": ">=8.0", "topthink/think-container": ">=3.0"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think validate", "support": {"issues": "https://github.com/top-think/think-validate/issues", "source": "https://github.com/top-think/think-validate/tree/v3.0.3"}, "time": "2025-01-07T08:18:42+00:00"}, {"name": "topthink/think-workflow", "version": "2.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-workflow.git", "reference": "3571873956f61baad5a1321e3a7aa7ca644d8077"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-workflow/zipball/3571873956f61baad5a1321e3a7aa7ca644d8077", "reference": "3571873956f61baad5a1321e3a7aa7ca644d8077", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.0", "symfony/workflow": "^5.0", "topthink/framework": "^6.0|^8.0"}, "require-dev": {"topthink/think-annotation": "^2.0", "topthink/think-ide-helper": "v1.0"}, "type": "library", "extra": {"think": {"services": ["think\\workflow\\Service"]}}, "autoload": {"psr-4": {"think\\workflow\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Workflow For ThinkPHP6", "support": {"issues": "https://github.com/top-think/think-workflow/issues", "source": "https://github.com/top-think/think-workflow/tree/2.0"}, "time": "2023-10-27T07:09:07+00:00"}, {"name": "topthinkcloud/client", "version": "v1.1.6", "source": {"type": "git", "url": "https://git.topthink.com/topteam/client.git", "reference": "1d7369c831ab9b5b6824d96f968d701469f830b6"}, "require": {"firebase/php-jwt": "^6.1", "php-http/cache-plugin": "^1.7.1", "php-http/client-common": "^2.3", "php-http/discovery": "^1.12", "php-http/httplug": "^2.2", "php-http/multipart-stream-builder": "^1.1.2", "psr/cache": "^1.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0", "symfony/options-resolver": ">=6.4", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"guzzlehttp/guzzle": "^7.2", "http-interop/http-factory-guzzle": "^1.0", "topthink/framework": "^6.0", "yunwuxin/think-notification": "^3.0"}, "type": "library", "extra": {"think": {"services": ["TopThinkCloud\\Service"], "config": {"cloud": "src/config.php"}}}, "autoload": {"psr-4": {"TopThinkCloud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2024-11-22T13:37:24+00:00"}, {"name": "twig/twig", "version": "v3.7.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "a0ce373a0ca3bf6c64b9e3e2124aca502ba39554"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/a0ce373a0ca3bf6c64b9e3e2124aca502ba39554", "reference": "a0ce373a0ca3bf6c64b9e3e2124aca502ba39554", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.3"}, "type": "library", "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.7.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2023-08-28T11:09:02+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "yohang88/letter-avatar", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/yohang88/letter-avatar.git", "reference": "15cb7298239be8c69c7982bd319b8170d650774b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yohang88/letter-avatar/zipball/15cb7298239be8c69c7982bd319b8170d650774b", "reference": "15cb7298239be8c69c7982bd319b8170d650774b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "intervention/image": "^2.3", "php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "4.*", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"YoHang88\\LetterAvatar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Yoga Hanggara", "email": "<EMAIL>", "homepage": "https://yohang.net", "role": "Developer"}], "description": "Generate user avatar using name initials letter.", "homepage": "https://github.com/yohang88/letter-avatar", "keywords": ["avatar", "letter avatar", "profile picture"], "support": {"issues": "https://github.com/yohang88/letter-avatar/issues", "source": "https://github.com/yohang88/letter-avatar/tree/2.2.0"}, "time": "2019-10-03T03:14:25+00:00"}, {"name": "yunwuxin/think-auth", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/yunwuxin/think-auth.git", "reference": "0f8776ad42c085ec9280a85ecbd1d54247e336ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yunwuxin/think-auth/zipball/0f8776ad42c085ec9280a85ecbd1d54247e336ea", "reference": "0f8776ad42c085ec9280a85ecbd1d54247e336ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"yunwuxin/think-mail": "^3.0", "yunwuxin/think-notification": "^3.0"}, "suggest": {"yunwuxin/think-mail": "Required to send email.", "yunwuxin/think-notification": "Required to send notification."}, "default-branch": true, "type": "think-extend", "extra": {"think": {"services": ["yunwuxin\\auth\\Service"], "config": {"auth": "src/config.php"}}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"yunwuxin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Auth Library For ThinkPHP5", "support": {"issues": "https://github.com/yunwuxin/think-auth/issues", "source": "https://github.com/yunwuxin/think-auth/tree/v3.0.7"}, "time": "2023-07-01T11:04:37+00:00"}, {"name": "yunwuxin/think-chunk-upload", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/yunwuxin/think-chunk-upload.git", "reference": "9490bded911f79725873f803775318d7058e4b76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yunwuxin/think-chunk-upload/zipball/9490bded911f79725873f803775318d7058e4b76", "reference": "9490bded911f79725873f803775318d7058e4b76", "shasum": ""}, "require": {"guzzlehttp/guzzle": ">=7.0", "ramsey/uuid": ">=3.9", "symfony/filesystem": ">=5.4", "topthink/framework": "^6.0|^8.0"}, "type": "library", "autoload": {"psr-4": {"think\\ChunkUpload\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/yunwuxin/think-chunk-upload/issues", "source": "https://github.com/yunwuxin/think-chunk-upload/tree/v1.0.4"}, "time": "2023-10-27T07:17:11+00:00"}, {"name": "yunwuxin/think-cron", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/yunwuxin/think-cron.git", "reference": "b54baa2c7a6ad68653d9a1e0a9b22e18ac0bcbe7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yunwuxin/think-cron/zipball/b54baa2c7a6ad68653d9a1e0a9b22e18ac0bcbe7", "reference": "b54baa2c7a6ad68653d9a1e0a9b22e18ac0bcbe7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"dragonmantank/cron-expression": "^3.0", "nesbot/carbon": "^2.28", "symfony/process": ">=4.2", "topthink/framework": "^6.0 || ^8.0"}, "require-dev": {"topthink/think-swoole": "^4.0"}, "default-branch": true, "type": "library", "extra": {"think": {"config": {"cron": "src/config.php"}, "services": ["yunwuxin\\cron\\Service"]}}, "autoload": {"psr-4": {"yunwuxin\\cron\\": "src/cron"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "计划任务", "support": {"issues": "https://github.com/yunwuxin/think-cron/issues", "source": "https://github.com/yunwuxin/think-cron/tree/v3.0.7"}, "time": "2023-08-11T12:34:39+00:00"}, {"name": "yunwuxin/think-social", "version": "3.1.x-dev", "source": {"type": "git", "url": "https://github.com/yunwuxin/think-social.git", "reference": "7562c08625995fbbc208ec038170d3969c9866f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yunwuxin/think-social/zipball/7562c08625995fbbc208ec038170d3969c9866f5", "reference": "7562c08625995fbbc208ec038170d3969c9866f5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3|^7.2", "topthink/framework": "^6.0|^8.0"}, "default-branch": true, "type": "think-extend", "extra": {"think": {"services": ["yunwuxin\\social\\Service"], "config": {"social": "src/config.php"}}}, "autoload": {"files": ["src/common.php"], "psr-4": {"yunwuxin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ThinkPHP6 Social SDK", "support": {"issues": "https://github.com/yunwuxin/think-social/issues", "source": "https://github.com/yunwuxin/think-social/tree/v3.1.2"}, "time": "2023-10-10T06:02:27+00:00"}, {"name": "yunwuxin/think-twig", "version": "v3.0.8", "source": {"type": "git", "url": "https://github.com/yunwuxin/think-twig.git", "reference": "08b4f38d17747d86ec3f4f6b602377ec7d69bde1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yunwuxin/think-twig/zipball/08b4f38d17747d86ec3f4f6b602377ec7d69bde1", "reference": "08b4f38d17747d86ec3f4f6b602377ec7d69bde1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "^6.0 || ^8.0", "twig/twig": "^3.1.1"}, "type": "library", "autoload": {"files": ["src/help.php"], "psr-4": {"yunwuxin\\twig\\": "src/"}, "classmap": ["src/Twig.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Twig Template For ThinkPHP5", "support": {"issues": "https://github.com/yunwuxin/think-twig/issues", "source": "https://github.com/yunwuxin/think-twig/tree/v3.0.8"}, "time": "2023-07-01T10:58:50+00:00"}], "packages-dev": [{"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419", "reference": "3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.3"}, "time": "2023-08-12T11:01:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.24.2", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "bcad8d995980440892759db0c32acae7c8e79442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/bcad8d995980440892759db0c32acae7c8e79442", "reference": "bcad8d995980440892759db0c32acae7c8e79442", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.2"}, "time": "2023-09-26T12:28:12+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.11", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "ee14c8254a480913268b1e3b1cba8045ed122694"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/ee14c8254a480913268b1e3b1cba8045ed122694", "reference": "ee14c8254a480913268b1e3b1cba8045ed122694", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-08-30T16:03:21+00:00"}, {"name": "topthink/think-dumper", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/top-think/think-dumper.git", "reference": "f0c7d1d0a76bfe6d279c902e75b01181ecb1c674"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-dumper/zipball/f0c7d1d0a76bfe6d279c902e75b01181ecb1c674", "reference": "f0c7d1d0a76bfe6d279c902e75b01181ecb1c674", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/var-dumper": ">=6.0", "topthink/framework": "^6.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^11.4"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\dumper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Dumper extend for thinkphp", "support": {"issues": "https://github.com/top-think/think-dumper/issues", "source": "https://github.com/top-think/think-dumper/tree/v1.0.3"}, "time": "2025-01-09T07:32:12+00:00"}, {"name": "topthink/think-ide-helper", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/top-think/think-ide-helper.git", "reference": "46900740372c2cca27fdf55c8d5e3a40bc13bab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-ide-helper/zipball/46900740372c2cca27fdf55c8d5e3a40bc13bab8", "reference": "46900740372c2cca27fdf55c8d5e3a40bc13bab8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ergebnis/classy": "^1.1", "phpdocumentor/reflection-docblock": "^5.0", "topthink/framework": "^6.0 || ^8.0"}, "default-branch": true, "type": "library", "extra": {"think": {"services": ["think\\ide\\Service"]}}, "autoload": {"psr-4": {"think\\ide\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-ide-helper/issues", "source": "https://github.com/top-think/think-ide-helper/tree/v1.0.4"}, "time": "2023-07-01T11:13:08+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"topthink/framework": 20, "topthink/gitlib": 20, "topthink/think-agent": 20, "topthink/think-annotation": 20, "topthink/think-ide-helper": 20, "topthink/think-orm": 20, "topthink/think-queue": 20, "topthink/think-swoole": 20, "topthink/think-workflow": 20, "yunwuxin/think-auth": 20, "yunwuxin/think-cron": 20, "yunwuxin/think-social": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.0", "ext-json": "*", "ext-inotify": "*", "ext-fileinfo": "*"}, "platform-dev": {}, "platform-overrides": {"ext-swoole": "4.6.0", "ext-inotify": "3.0.0", "ext-fileinfo": "1.0.4"}, "plugin-api-version": "2.6.0"}