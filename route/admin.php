<?php

use think\facade\Route;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authentication;
use yunwuxin\auth\middleware\UseGuard;

Route::group('admin/api', function () {
    Route::group(function () {
        Route::get('current', function (Auth $auth) {
            return $auth->user();
        });

        Route::get('space', 'space.index/index');
        Route::post('space/:id/open', 'space.index/open');
        Route::delete('space/:id', 'space.index/delete');

        Route::get('book/search', 'book/search');
        Route::get('user/search', 'user/search');

        Route::resource('app', 'app');
    })->middleware(Authentication::class);

    Route::get('login', 'login/index');
    Route::post('login', 'login/save');
})->prefix('admin.')->middleware(UseGuard::class, 'admin');
