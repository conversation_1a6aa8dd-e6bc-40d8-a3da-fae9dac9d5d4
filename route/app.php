<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use app\middleware\Authentication;
use think\facade\Route;

Route::group(function () {
    Route::redirect('/', '/workspace');
    Route::get('workspace', 'workspace/index');
    Route::delete('book/:id', 'book/delete');
    Route::get('book/edit', 'book/edit');
    Route::get('auth/logout', 'auth/logout');
})->middleware(Authentication::class);

Route::post('open', 'open/index');
Route::get('sandbox', 'sandbox/index');
Route::get('auth/login', 'auth/login');
