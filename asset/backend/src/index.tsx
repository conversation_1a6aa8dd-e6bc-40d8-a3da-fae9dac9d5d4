import { createRoot } from 'react-dom/client';
import '@topthink/common/scss/app.scss';
import { createApplication, request } from '@topthink/common';
import routes from '@/routes';

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);

    const App = createApplication({
        basename: '/admin',
        baseURL: '/admin/api',
        async userResolver() {
            return await request('/current');
        },
        async onLogin(token) {
            const result = await request({
                url: '/login',
                method: 'POST',
                data: {
                    token
                }
            });
            return result.token;
        },
        async onUnauthorized() {
            const { url } = await request('/login');
            return url;
        }
    });

    root.render(<App routes={routes} />);
}
