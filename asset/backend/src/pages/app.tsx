import { Columns, Content, FormProps, ModalForm, Space, Table } from '@topthink/common';

export default function App() {

    const schema: FormProps['schema'] = {
        type: 'object',
        required: [
            'title',
            'name',
            'type',
        ],
        properties: {
            title: {
                title: '名称',
                type: 'string'
            },
            name: {
                title: '标识',
                type: 'string'
            },
            type: {
                title: '类型',
                type: 'string',
                enum: ['gitlab', 'gitee', 'github', 'topthink'],
                default: 'gitlab'
            },
            url: {
                title: '应用地址',
                type: 'string',
                description: 'https://github.com',
            },
            client_id: {
                title: 'Client ID',
                type: 'string'
            },
            client_secret: {
                title: 'Client Secret',
                type: 'string'
            },
            expire_time: {
                title: '有效期',
                type: 'string',
                description: '留空则永久有效'
            },
            remark: {
                title: '备注',
                type: 'string'
            },
            login: {
                title: '登录显示',
                type: 'boolean',
            },
            proxy: {
                title: '使用代理',
                type: 'boolean',
            },
        }
    };

    const uiSchema: FormProps['uiSchema'] = {
        type: {
            'ui:enumNames': ['Gitlab', 'Gitee', 'Github', 'Topthink']
        },
        expire_time: {
            'ui:widget': 'date'
        },
        remark: {
            'ui:widget': 'textarea'
        }
    };

    const columns: Columns = [
        {
            title: '名称',
            dataIndex: 'title',
        },
        {
            title: '标识',
            dataIndex: 'name',
        },
        {
            title: '类型',
            dataIndex: 'type',
        },
        {
            title: '应用地址',
            dataIndex: 'url',
        },
        {
            title: '有效期',
            dataIndex: 'expire_time',
            render({ value }) {
                return value || '永久';
            }
        },
        {
            title: 'API令牌',
            dataIndex: 'token',
        },
        {
            title: '操作',
            align: 'right',
            key: 'action',
            render({ record, action }) {
                return <Space>
                    <ModalForm
                        modalProps={{ size: 'lg', header: '编辑应用' }}
                        action={`/app/${record.id}`}
                        method={'put'}
                        onSuccess={action.reload}
                        schema={schema}
                        formData={record}
                        uiSchema={uiSchema}
                        text={'编辑'}
                    />
                </Space>;
            }
        }
    ];

    return <Content>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    modalProps={{ size: 'lg' }}
                    action={'/app'}
                    onSuccess={reload}
                    schema={schema}
                    uiSchema={uiSchema}
                    text={'创建应用'}
                />;
            }}
            source={'/app'}
            columns={columns}
        />
    </Content>;
}
