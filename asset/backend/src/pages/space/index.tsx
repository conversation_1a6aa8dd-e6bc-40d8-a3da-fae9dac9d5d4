import { Content, Table, Space as RcSpace, But<PERSON>, Modal, request, RequestButton } from '@topthink/common';
import { MouseEvent } from 'react';

export default function Space() {

    const handleSubmit = async (e: MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        const button = e.currentTarget;
        const id = button.dataset.id;
        const form = button.form;
        if (form && await Modal.confirm({ message: '确定要打开吗？' })) {
            const result = await request({
                url: `/space/${id}/open`,
                method: 'post',
            });

            form.action = result.url;
            form.token.value = result.token;

            form.submit();
        }
    };


    return <Content>
        <Table
            source={`/space`}
            search={{
                fields: [],
                extraFields: {
                    book: '文档',
                    user: '用户',
                },
                ui: {
                    book: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/book/search',
                        }
                    },
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/user/search',
                        }
                    },
                }
            }}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'hash_id',
                    width: 100
                },
                {
                    title: '应用',
                    dataIndex: ['user', 'app', 'title'],
                    width: 100,
                },
                {
                    title: '用户',
                    dataIndex: ['user', 'name'],
                    width: 150,
                },
                {
                    title: '文档',
                    dataIndex: ['book', 'name'],
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    width: 80,
                    render({ record }) {
                        if (record.is_online) {
                            return <span className='text-success'>在线</span>;
                        }
                        return <span className='text-muted'>离线</span>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <RcSpace>
                            <form method='post' target='_blank'>
                                <input type='hidden' name='token' />
                                <Button data-id={record.id} onClick={handleSubmit}>打开</Button>
                            </form>
                            <RequestButton
                                url={`/space/${record.id}`}
                                method='delete' confirm='这将清空用户的工作目录，确定要重置吗？'
                                onSuccess={action.reload} disabled={record.is_online}>重置</RequestButton>
                        </RcSpace>;
                    }
                }
            ]}
        />
    </Content>;
}
