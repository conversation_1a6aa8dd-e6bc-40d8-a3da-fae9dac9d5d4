import { <PERSON><PERSON>, <PERSON>, SiderLayout } from '@topthink/common';
import { useMemo } from 'react';
import { Dropdown } from 'react-bootstrap';
import logoSrc from './images/logo.svg';

export default function Layout() {

    const menus = useMemo(() => {
        return <>
            <Dropdown.Item as={Link} to='/logout'>退出登录</Dropdown.Item>
        </>;
    }, []);

    return <>
        <Header menus={menus} className={'px-3'} logo={false}>
            <Link className='navbar-brand d-flex align-items-center' to='/'>
                <img className={'me-3 rounded'} src={logoSrc} width={25} height={25} />
                云写作
            </Link>
            <ul className='navbar-nav me-auto'>

            </ul>
        </Header>
        <SiderLayout title='系统设置' />
    </>;
}
