import { Navigate, RouteObject } from '@topthink/common';
import Layout from './layout';
import Dashboard from './pages/dashboard';
import App from './pages/app';
import Space from './pages/space';

const routes: RouteObject[] = [
    {
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Navigate to='dashboard' replace />
            },
            {
                path: 'dashboard',
                element: <Dashboard />,
                meta: {
                    title: '仪表盘',
                    icon: 'speedometer2'
                }
            },
            {
                path: 'space',
                element: <Space />,
                meta: {
                    title: '工作区管理',
                    icon: 'person-workspace'
                }
            },
            {
                path: 'app',
                element: <App />,
                meta: {
                    title: '应用管理',
                    icon: 'app'
                }
            }
        ]
    }
];

export default routes;
