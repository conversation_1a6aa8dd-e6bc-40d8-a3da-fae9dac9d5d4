{"name": "@topwrite/x-backend", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "dependencies": {"@topthink/common": "^1.2.42", "bootstrap": "^5.0.2", "react-bootstrap": "^2.1.2"}, "devDependencies": {"@topthink/webpack-config-plugin": "^1.0.20", "@types/lodash": "^4.14.161", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "babel-plugin-styled-components": "^1.13.1", "typescript": "^4.6.4", "webpack": "^5.24.4", "webpack-cli": "^4.5.0", "webpack-dev-server": "^4.7.4"}}