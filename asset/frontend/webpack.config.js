const path                    = require("path");
const WebpackConfigPlugin     = require("@topthink/webpack-config-plugin");
const {WebpackManifestPlugin} = require("webpack-manifest-plugin");
const CopyWebpackPlugin       = require("copy-webpack-plugin");

module.exports = (env) => {

    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/app.ts",
        output   : {
            filename     : "app.[contenthash:6].js",
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            publicPath   : "/asset/",
            clean        : true
        },
        resolve  : {
            fallback: {
                "path": "path-browserify"
            }
        },
        externals: {
            jquery: "jQuery"
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : {
                    filename     : "../../../../view/base.twig",
                    template     : "public/index.html",
                    inject       : false,
                    scriptLoading: "blocking",
                    minify       : false,
                    favicon      : "public/favicon.ico"
                },
                react: false
            }),
            new CopyWebpackPlugin({
                patterns: [
                    {
                        from: "public/images",
                        to  : "images/[path][name].[contenthash:6][ext]"
                    },
                ]
            }),
            new WebpackManifestPlugin({
                writeToFileEmit: true,
                filter(file) {
                    return file.isModuleAsset;
                }
            })
        ],
        devServer: {
            hot          : false,
            allowedHosts : "all",
            devMiddleware: {
                writeToDisk(filePath) {
                    return /\.twig$/.test(filePath);
                }
            }
        }
    };
};
