<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

//think-auth 配置文件

return [
    'default'          => 'app',
    'guards'           => [
        'app'   => [
            'type'     => 'session',
            'provider' => 'user',
        ],
        'admin' => [
            'type'     => 'token',
            'provider' => 'admin',
        ],
        'space' => [
            'type'     => 'session',
            'provider' => 'space',
        ],
    ],
    'providers'        => [
        'user'  => [
            'type' => \app\lib\auth\UserProvider::class,
        ],
        'admin' => [
            'type' => \app\lib\auth\AdminProvider::class,
        ],
        'space' => [
            'type' => \app\lib\auth\SpaceProvider::class,
        ],
    ],
    //设为false,则不注册路由
    'route'            => false,
    'policy_namespace' => '\\app\\policy\\',
    'policies'         => [],
];
