<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateReleaseTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('release')
             ->addColumn(Column::string('sha'))
             ->addColumn(Column::integer('book_id'))
             ->addColumn(Column::integer('user_id'))
             ->addColumn(Column::text('message')->setNullable())
             ->addTimestamps()
             ->addIndex('sha')
             ->addIndex('book_id')
             ->create();

        $this->table('release_log')
             ->addColumn(Column::integer('release_id'))
             ->addColumn(Column::string('type'))
             ->addColumn(Column::tinyInteger('status')->setDefault(0))
             ->addColumn(Column::longText('trace')->setNullable())
             ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
             ->addColumn(Column::timestamp('start_time')->setNullable())
             ->addColumn(Column::timestamp('end_time')->setNullable())
             ->create();
    }
}
