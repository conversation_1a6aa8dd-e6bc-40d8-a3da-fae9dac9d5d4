<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookMemberTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('book_member')
             ->setId(false)
             ->addColumn(Column::integer('book_id'))
             ->addColumn(Column::integer('user_id'))
             ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
             ->create();

        $this->table('book')
             ->addColumn(Column::string('channel')->setAfter('user_id'))
             ->addIndex('channel')
             ->update();

        \app\model\Book::chunk(10, function ($books) {
            /** @var \app\model\Book $book */
            foreach ($books as $book) {
                if ($book->user_id > 0) {
                    $user = \app\model\User::find($book->user_id);
                    $book->members()->attach($user);
                    $channel = $user->channel;
                } else {
                    $channel = 'virtual';
                }

                $book->save([
                    'channel' => $channel,
                ]);
            }
        });
    }

    public function down()
    {
        $this->dropTable('book_member');
        $this->table('book')
             ->removeColumn('channel');
    }
}
