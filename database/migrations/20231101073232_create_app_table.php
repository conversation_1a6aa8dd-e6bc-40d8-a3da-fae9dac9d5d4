<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateAppTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('app')
            ->addColumn(Column::string('title'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('type'))
            ->addColumn(Column::string('url')->setNullable())
            ->addColumn(Column::string('client_id')->setNullable())
            ->addColumn(Column::string('client_secret')->setNullable())
            ->addColumn(Column::timestamp('expire_time')->setNullable())
            ->addColumn(Column::boolean('proxy')->setDefault(false))
            ->addColumn(Column::boolean('login')->setDefault(false))
            ->addColumn(Column::text('remark')->setNullable())
            ->addColumn(Column::string('token'))
            ->addTimestamps()
            ->create();
    }
}
