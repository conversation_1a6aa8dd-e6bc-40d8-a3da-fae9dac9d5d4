<?php

namespace app\task;

use app\lib\Date;
use app\model\Space;
use app\model\VirtualUser;
use Exception;
use Symfony\Component\Filesystem\Path;
use Symfony\Component\Process\Process;
use think\db\Query;
use yunwuxin\cron\Task;

class ClearOutdatedRepoTask extends Task
{

    protected $root = '/opt/repo';

    protected function configure()
    {
        $this->everyMinute();
        $this->withoutOverlapping(60 * 5);
        $this->onOneServer();
    }

    protected function handle()
    {
        $space = Space::where(function (Query $query) {
            $query->whereOr(function (Query $query) {
                $query->where('user_id', '>', 0);
                $query->where('update_time', '<', Date::now()->subMonths(3));
            });
            $query->whereOr(function (Query $query) {
                $query->where('user_id', '=', 0);
                $query->where('update_time', '<', Date::now()->subDays(3));
            });
        })
            ->whereRaw("`update_time` > COALESCE(`gc_time`,'1970-01-01 00:00:00')")
            ->find();

        if ($space) {
            try {
                $this->runProcess('mkdir -p outdated', $this->root, false);

                if ($space->book && $space->user instanceof VirtualUser) {
                    //sandbox
                    //清理远程仓库
                    $remotePath = $space->user->getRemotePath();
                    $this->moveDir($remotePath);
                }

                //清理本地仓库
                $repoPath = $space->getRepoPath();
                $this->moveDir($repoPath);
                @rmdir(dirname($repoPath));

                //清理发布日志
                if ($space->book) {
                    $space->book->gc();
                }

                $space->isAutoWriteTimestamp(false)->save([
                    'gc_time' => Date::now(),
                ]);

                $this->runProcess('rm -rf outdated/*', $this->root, false);

                trace("space [{$space->id}] gc succeed", 'info');
            } catch (Exception $e) {
                trace("space [{$space->id}] gc failed: " . $e->getMessage(), 'error');
            }
        }
    }

    protected function moveDir($dir)
    {
        if ($dir && is_dir($dir)) {
            $target = Path::join($this->root, 'outdated', uniqid());
            $this->runProcess("mv {$dir} {$target}", $this->root, false);
        }
    }

    protected function runProcess($command, $cwd, $must = true)
    {
        $process = Process::fromShellCommandline($command, $cwd, timeout: 60);
        if ($must) {
            $process->mustRun();
        } else {
            $process->run();
        }

        return trim($process->getOutput());
    }
}
