<?php

namespace app\command;

use think\ChunkUpload\Client;
use think\console\Command;
use think\console\Output;
use think\facade\Log;
use think\File;

class Lfs extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('git:lfs');
    }

    public function handle()
    {
        while ($f = fgets(STDIN)) {
            $request = json_decode($f);

            try {
                switch ($request->event) {
                    case 'init':
                        $this->output->writeln('{}');
                        break;
                    case 'download':
                        throw new \Exception('not support download direction');
                    case 'upload':
                        $client = new Client($request->action->href, 'PUT', [
                            'Authorization' => $request->action->header->Authorization,
                        ]);

                        $client->upload(new File($request->path));

                        $this->response([
                            'event' => 'complete',
                            'oid'   => $request->oid,
                        ]);

                        break;
                    case 'terminate':
                        break 2;
                }
            } catch (\Exception $e) {
                //记录错误
                Log::write($e->getMessage());
                Log::write($e->getTraceAsString());

                $data = [];
                if ($request->oid) {
                    $data['event'] = 'complete';
                    $data['oid']   = $request->oid;
                }

                $this->response(array_merge($data, ['error' => ['code' => 0, 'message' => $e->getMessage()]]));
            }
        }
    }

    protected function response($data = [])
    {
        $this->output->writeln(json_encode(new \ArrayObject($data)), Output::OUTPUT_RAW);
    }
}
