<?php

namespace app\command;

use app\model\Space;
use DirectoryIterator;
use think\console\Command;

class Clear extends Command
{
    protected $root = '/opt/repo';

    protected function configure()
    {
        $this->setName('app:clear');
    }

    public function handle()
    {
        $scanned = array_filter(explode("\n", @file_get_contents($this->root . '/scanned')));

        $excludedDirs = ['0', 'clone', 'lfs', 'sandbox', 'outdated'];

        $iterator = new DirectoryIterator($this->root);

        /** @var DirectoryIterator $dir */
        foreach ($iterator as $dir) {
            $user = $dir->getFilename();
            if ($dir->isDot() || !$dir->isDir() || in_array($user, $excludedDirs) || !is_numeric($user)) {
                continue;
            }

            $subIterator = new DirectoryIterator($dir->getRealPath());
            /** @var DirectoryIterator $subDir */
            foreach ($subIterator as $subDir) {
                $book     = $subDir->getFilename();
                $pathname = "{$user}/{$book}";
                if ($subDir->isDot() || !$subDir->isDir() || !is_numeric($book)) {
                    continue;
                }

                if (in_array($pathname, $scanned)) {
                    $this->output->warning("{$pathname} scanned");
                    continue;
                }

                $space = Space::where('user_id', $user)->where('book_id', $book)->find();

                if ($space) {
                    $this->output->warning("{$pathname} skip");
                } else {
                    $time = date('Y-m-d H:i:s', $subDir->getMTime());
                    $this->output->info("{$pathname} start {$time}");

                    Space::create([
                        'user_id'     => $user,
                        'book_id'     => $book,
                        'create_time' => $time,
                        'update_time' => $time,
                    ]);

                    $this->output->info("{$pathname} done");
                }

                file_put_contents($this->root . '/scanned', $pathname . "\n", FILE_APPEND);
            }
        }
    }
}
