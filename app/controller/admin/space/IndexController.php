<?php

namespace app\controller\admin\space;

use app\controller\admin\Controller;
use app\model\Space;

class IndexController extends Controller
{
    public function index()
    {
        $query = Space::order('update_time', 'desc')
            ->with(['user', 'book'])
            ->append(['is_online', 'is_ready', 'hash_id', 'user.app']);

        $this->filterFields($query, [
            'user' => function ($query, $value) {
                $query->where('user_id', $value);
            },
            'book' => function ($query, $value) {
                $query->where('book_id', $value);
            },
        ]);

        return $query->paginate();
    }

    public function open($id)
    {
        $space = Space::findOrFail($id);

        return [
            'url'   => $space->url,
            'token' => $space->token,
        ];
    }

    public function delete($id)
    {
        $space = Space::findOrFail($id);

        $space->reset();
    }
}
