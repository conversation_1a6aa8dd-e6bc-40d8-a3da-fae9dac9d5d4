<?php

namespace app\controller\admin;

use app\model\User;

class UserController extends Controller
{
    public function search()
    {
        $query = User::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('name|email', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (User $user) {
            return [
                'label' => "{$user->name}[{$user->email}]",
                'value' => $user->id,
            ];
        });
    }
}
