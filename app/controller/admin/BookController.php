<?php

namespace app\controller\admin;

use app\model\Book;

class BookController extends Controller
{
    public function search()
    {
        $query = Book::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('name', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (Book $book) {
            return [
                'label' => $book->name,
                'value' => $book->id,
            ];
        });
    }
}
