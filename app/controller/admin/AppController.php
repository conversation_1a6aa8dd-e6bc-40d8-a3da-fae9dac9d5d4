<?php

namespace app\controller\admin;

use app\model\App;
use think\helper\Str;

class AppController extends Controller
{
    public function index()
    {
        return App::order('id desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'type'          => 'require',
            'title'         => 'require',
            'name'          => 'require|unique:' . App::class,
            'url'           => '',
            'client_id'     => '',
            'client_secret' => '',
            'expire_time'   => 'date',
            'remark'        => '',
            'login'         => 'boolean',
            'proxy'         => 'boolean',
        ]);

        $data['token'] = Str::random(32);

        App::create($data);
    }

    public function update($id)
    {
        $app = App::findOrFail($id);

        $data = $this->validate([
            'type'          => 'require ',
            'title'         => 'require ',
            'name'          => 'require|unique:' . App::class,
            'url'           => '',
            'client_id'     => '',
            'client_secret' => '',
            'expire_time'   => 'date',
            'remark'        => '',
            'login'         => 'boolean',
            'proxy'         => 'boolean',
        ]);

        $app->save($data);
    }
}
