<?php

namespace app\controller\admin;

use app\lib\Cloud;
use app\model\Admin;

class LoginController extends Controller
{
    public function index(Cloud $cloud)
    {
        if ($cloud->isAvailable()) {
            return ['url' => $cloud->getAuthorizeUrl()];
        }
    }

    public function save(Cloud $cloud)
    {
        $data = $this->validate([
            'token' => 'require',
        ]);

        $userInfo = $cloud->getUserInfo($data['token']);

        if (!$userInfo['is_admin']) {
            abort(403, '无权限');
        }

        $admin = Admin::where('openid', $userInfo['id'])->where('channel', 'topthink')->find();

        if (!$admin) {
            $admin = new Admin([
                'openid'  => $userInfo['id'],
                'channel' => 'topthink',
            ]);
        }

        $admin->save([
            'name'   => $userInfo['name'],
            'avatar' => $userInfo['avatar'],
            'email'  => $userInfo['email'],
        ]);

        return [
            'token' => $admin->token,
        ];
    }
}
