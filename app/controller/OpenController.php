<?php

namespace app\controller;

use app\BaseController;
use app\lib\Date;
use app\model\App;
use app\model\Book;
use app\model\BookMember;
use app\model\Space;
use app\model\User;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\helper\Arr;
use think\helper\Str;
use TopThinkCloud\Client;

class OpenController extends BaseController
{
    public function index(Client $client)
    {
        $data = $this->validate([
            'channel' => 'require',
            'token'   => 'require',
        ]);

        try {
            if (Str::startsWith($data['channel'], 'license#')) {
                [, $id] = explode('#', $data['channel']);

                $info = $client->authenticate()->license()->show('wiki', $id);

                $expireTime = new Date($info['expire_time']);

                if ($expireTime->isPast()) {
                    abort(403, 'License expired');
                }

                $token = $info['code'];

                //创建app
                $app = App::where('name', $data['channel'])->find();
                if (empty($app)) {
                    App::create([
                        'name'  => $data['channel'],
                        'type'  => 'topthink',
                        'title' => $info['name'],
                        'url'   => $info['url'],
                    ]);
                } else {
                    $app->save([
                        'title' => $info['name'],
                        'url'   => $info['url'],
                    ]);
                }
            } else {
                $channel = $data['channel'];
                //多空间模式
                if (Str::contains($channel, '@')) {
                    [$channel,] = explode('@', $channel);
                }

                $app   = App::where('name', $channel)->findOrFail();
                $token = $app->token;
            }

            $decoded = (array) JWT::decode($data['token'], new Key($token, 'HS256'));

            $user = User::get($data['channel'], (array) $decoded['user']);
            $book = Book::get($data['channel'], (array) $decoded['book']);

            $permissions = (array) $decoded['permissions'];

            $accessLevel = match (true) {
                Arr::get($permissions, 'admin', false) => BookMember::OWNER,
                Arr::get($permissions, 'release', false) => BookMember::MAINTAINER,
                Arr::get($permissions, 'write', false) => BookMember::WRITER,
                default => BookMember::READER,
            };

            $book->members()->attach($user, ['access_level' => $accessLevel]);

            $space = Space::get($book, $user);

            return view('open')->assign('space', $space);
        } catch (Exception $e) {
            abort(403, $e->getMessage());
        }
    }
}
