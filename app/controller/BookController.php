<?php

namespace app\controller;

use app\model\Space;
use Exception;
use think\exception\HttpException;
use think\exception\ValidateException;

class BookController extends Controller
{
    public function edit($url)
    {
        $client = $this->user->getClient();

        try {
            $book = $client->getBookByUrl($url);

            //检查space
            $space = Space::get($book, $this->user);

            return view('open')->assign('space', $space);
        } catch (ValidateException $e) {
            throw new HttpException(404, $e->getMessage());
        }
    }

    public function delete($id)
    {
        $this->user->books()->detach($id);
    }

}
