<?php

namespace app\controller;

use app\BaseController;
use app\model\User;
use app\Request;
use think\View;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    protected ?User $user;

    public function __construct(Request $request, View $view, Auth $auth)
    {
        parent::__construct($request);

        $this->user = $auth->user();
        if ($this->request->isGet()) {
            $view->assign('request', $this->request);
            $view->assign('user', $this->user);
        }
    }

}
