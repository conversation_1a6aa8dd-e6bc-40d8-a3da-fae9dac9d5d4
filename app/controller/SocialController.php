<?php

namespace app\controller;

use app\model\User;
use think\Request;
use yunwuxin\Auth;
use yunwuxin\Social;
use yunwuxin\social\SocialControllerTrait;

class SocialController
{
    use SocialControllerTrait;

    public function handleSocialCallback(Request $request, Social $social, Auth $auth, $channel)
    {
        $socialUser = $social->channel($channel)->user();

        $nickname = $socialUser->getNickname();
        if (empty($nickname) && $channel === 'github') {
            $nickname = $socialUser->getRaw('login');
        }

        $user = User::get($channel, [
            'id'     => $socialUser->getId(),
            'name'   => $nickname,
            'email'  => $socialUser->getEmail(),
            'avatar' => $socialUser->getAvatar(),
            'token'  => (string) $socialUser->getToken(),
        ]);

        $auth->login($user);

        return redirect('/')->restore();
    }
}
