<?php

namespace app\controller\space;

use app\BaseController;
use app\model\Space;
use app\repo\Workspace;
use app\Request;
use think\exception\ValidateException;
use think\Session;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    protected ?Space $space;

    private ?Workspace $workspace = null;

    public function __construct(Request $request, Session $session, Auth $auth)
    {
        parent::__construct($request);

        $this->space = Space::retrieve($request, $session, $auth);
    }

    protected function getWorkspace()
    {
        if (is_null($this->workspace)) {
            try {
                $this->workspace = $this->space->createWorkspace();
            } catch (\Throwable) {
                throw new ValidateException('版本库尚未初始化，请从编辑页面进入');
            }
        }
        return $this->workspace;
    }
}
