<?php

namespace app\controller\space;

use think\ChunkUpload\Server;
use think\File;
use think\Request;

class ImportController extends Controller
{
    public function save($tid)
    {
        $metadata = $this->validate([
            'type'        => 'require',
            'source'      => ['require', function ($value, $data) {
                switch ($data['type']) {
                    case 'website':
                        if (empty($value['url'])) {
                            return '网页地址不能为空';
                        }
                        break;
                }
                return true;
            }],
            'independent' => '',
        ]);

        $workspace = $this->getWorkspace();

        $this->space->createImportJob($tid, $metadata, $workspace->getRootDir());
    }

    public function file(Request $request, Server $server)
    {
        return $server->serve($request, function (File $file, $metadata) {
            $file->move(runtime_path('temp/import'), $metadata['id'] . $metadata['ext']);
        });
    }

}
