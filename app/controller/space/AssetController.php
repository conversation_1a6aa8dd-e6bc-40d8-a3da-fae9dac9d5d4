<?php

namespace app\controller\space;

use Symfony\Component\Finder\Finder;
use think\ChunkUpload\Server;
use think\File;

class AssetController extends Controller
{

    public function index()
    {
        $dir = $this->getWorkspace()->getAssetPath();

        $files = [];

        if (is_dir($dir)) {
            $finder = new Finder();

            $finder
                ->files()
                ->in($dir)
                ->sortByChangedTime()
                ->reverseSorting();

            foreach ($finder as $file) {
                $files[] = [
                    'path'  => $file->getRelativePathname(),
                    'size'  => $file->getSize(),
                    'ctime' => $file->getCTime(),
                ];
            }
        }

        return json($files);
    }

    public function download($path)
    {
        $filename = $this->getWorkspace()->getAssetPath($path);
        if (!file_exists($filename)) {
            abort(404);
        }

        $eTag = sha1_file($filename);

        if ($this->request->isHead()) {
            $mimeType = mime_content_type($filename);
            return response()->contentType($mimeType)->header([
                'ETag'          => $eTag,
                'Cache-Control' => 'no-cache',
            ]);
        }

        if ($this->request->header('If-None-Match') == $eTag) {
            return response()->code(304);
        }

        return \think\swoole\helper\file($filename)->header([
            'ETag'          => $eTag,
            'Cache-Control' => 'no-cache',
        ]);
    }

    public function upload(Server $server, $path)
    {
        return $server->serve($this->request, function (File $file) use ($path) {
            $workspace = $this->getWorkspace();
            $dest      = $workspace->getAssetPath($path);
            $workspace->ensureDirExists($dest);
            rename($file->getRealPath(), $dest);
        });
    }
}
