<?php

namespace app\controller\space;

use ArrayObject;
use Carbon\Carbon;
use finfo;
use Symfony\Component\Filesystem\Path;
use YoHang88\LetterAvatar\LetterAvatar;

class PreviewController extends Controller
{
    public function index()
    {
        $path = $this->request->param('path') ?: 'index.html';

        $workspace = $this->getWorkspace();

        try {
            $config = $workspace->getConfig();

            if (Path::getExtension($path) == 'html') {
                $summary = $workspace->getSummary();

                if ($path == 'index.html') {
                    $filename = 'README.md';
                } else {
                    $filename = Path::changeExtension($path, 'md');
                }

                $file = [
                    'path' => $path,
                ];

                try {
                    $file['content'] = $workspace->readFile($filename, true);
                    $file['meta']    = [
                        'mtime' => Carbon::now()->toAtomString(),
                    ];
                } catch (\Exception) {

                }

                $payload = [
                    'id'           => $this->space->book->hash_id,
                    'metadata'     => new ArrayObject($this->space->book->metadata ?? []),
                    'summary'      => $summary,
                    'config'       => $config,
                    'file'         => $file,
                    'pluginCenter' => [
                        'host'   => config('app.plugins_host'),
                        'preset' => $this->space->getPresetPlugins(),
                    ],
                    'options'      => [
                        'preview'   => true,
                        'poweredBy' => $this->space->getPoweredBy(),
                    ],
                ];

                if ($this->request->isJson()) {
                    return json($payload);
                }

                $version = get_package_version('@topwrite/reader');

                return view('space/preview')
                    ->assign('version', $version)
                    ->assign('payload', json_encode($payload, JSON_UNESCAPED_UNICODE));
            }

            $content = $workspace->readFile($path);

            if (is_null($content)) {
                switch ($path) {
                    case 'logo.png': //自动生成logo
                        $content = (new LetterAvatar($config->getValue('title', 'Untitled'), 'square', 60))->encode();
                        break;
                    case '.topwrite/style.css':
                        $content = '';
                        break;
                }
            }

            if (is_null($content)) {
                abort(404);
            }

            $finfo    = new finfo(FILEINFO_MIME);
            $mimeType = $finfo->buffer($content);
            //TODO 视频文件
            return response($content)->contentType($mimeType);
        } finally {
            $workspace->close();
        }
    }
}
