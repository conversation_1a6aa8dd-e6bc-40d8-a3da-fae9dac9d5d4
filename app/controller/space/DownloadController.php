<?php

namespace app\controller\space;

use Symfony\Component\Filesystem\Path;
use think\Filesystem;
use think\helper\Str;
use think\swoole\response\File;

class DownloadController extends Controller
{
    public function index(Filesystem $filesystem)
    {
        $type    = $this->request->param('type');
        $book    = $this->space->book;
        $release = $book->getLastRelease();
        $log     = $release->getLog($type);

        if (!$log->isSucceed()) {
            abort(404);
        }

        $filename = $filesystem->path(Path::join('artifact', $book->getArtifact($type)));

        $file = new File($filename);

        $extension = strstr(pathinfo($filename)['basename'], '.');
        $sha       = Str::substr($release->sha, 0, 7);

        $file->setContentDisposition(File::DISPOSITION_ATTACHMENT, "book-{$sha}{$extension}");

        return $file;
    }
}
