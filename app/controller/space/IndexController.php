<?php

namespace app\controller\space;

use app\BaseController;
use app\model\Space;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use yunwuxin\Auth;

class IndexController extends BaseController
{
    public function index()
    {
        $version = get_package_version('@topwrite/editor');

        return view('space/editor')
            ->assign('version', $version);
    }

    public function open(Auth $auth)
    {
        $token = $this->request->post('token');

        if ($token) {
            try {
                $subDomain = $this->request->subDomain();
                $decoded   = (array) JWT::decode($token, new Key($subDomain, 'HS256'));

                $space = Space::findOrFail($decoded['id']);

                $auth->login($space);
            } catch (Exception) {
            }
        }

        return $this->index();
    }
}
