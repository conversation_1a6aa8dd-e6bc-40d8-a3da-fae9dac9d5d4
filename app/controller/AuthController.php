<?php

namespace app\controller;

use app\model\App;
use yunwuxin\Auth;

class AuthController extends Controller
{
    public function login()
    {
        if ($this->user) {
            return redirect('/');
        }

        $apps = App::where('login', true)->select();

        return view('auth/login')->assign('apps', $apps);
    }

    public function logout(Auth $auth)
    {
        $auth->logout();

        return redirect('/auth/login');
    }
}
