<?php

namespace app\model;

use Symfony\Component\Filesystem\Filesystem;
use think\model\concern\Virtual;
use topthink\git\Repository;

/**
 * Class app\model\VirtualUser
 *
 * @property-read mixed $app
 */
class VirtualUser extends User
{
    use Virtual;

    public static function createByOpenid($openid)
    {
        return new self([
            'id'      => 0,
            'openid'  => $openid,
            'name'    => 'Virtual User',
            'channel' => 'virtual',
        ]);
    }

    protected function getAppAttr()
    {
        return new VirtualApp([
            'name' => 'Virtual',
            'type' => 'virtual',
        ]);
    }

    public function getSpace()
    {
        $url = 'https://git.topthink.com/topteam/write/demo.git';

        $dir = '/opt/repo/sandbox/0/' . md5($url);

        if (!file_exists($dir)) {
            $filesystem = new Filesystem();
            if ($filesystem->exists($dir)) {
                $filesystem->remove($dir);
            }
            Repository::clone($dir, $url, ['--bare']);
        } else {
            $repo = new Repository($dir);
            $repo->pull();
        }

        $remotePath = $this->getRemotePath();

        if (!file_exists($remotePath)) {
            Repository::clone($remotePath, $dir, ['--bare']);
        }

        $book = Book::get('virtual', [
            'id'      => $this->openid,
            'name'    => 'sandbox',
            'url'     => 'https://x.topthink.com/sandbox',
            'git_url' => $remotePath,
        ]);

        return Space::get($book, $this);
    }

    public function getRemotePath()
    {
        return "/opt/repo/sandbox/{$this->openid}";
    }
}
