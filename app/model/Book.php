<?php

namespace app\model;

use app\lib\Date;
use Hashids\Hashids;
use Symfony\Component\Filesystem\Path;
use think\annotation\model\relation\HasMany;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\Book
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property array $metadata
 * @property int $id
 * @property int $user_id
 * @property string $channel
 * @property string $delete_time
 * @property string $lfs_url
 * @property string $name
 * @property string $openid
 * @property string $ssh_url
 * @property string $git_url
 * @property string $url
 * @property-read \app\model\BookMember $pivot
 * @property-read \app\model\Release[] $releases
 * @property-read \app\model\User[] $members
 * @property-read string $hash_id
 * @property-read string $hash_path
 * @method \think\model\relation\HasMany releases()
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
#[HasMany('releases', Release::class)]
class Book extends Model
{
    use SoftDelete;

    protected $type = [
        'metadata' => 'json',
    ];

    /**
     * @param $channel
     * @param array $data
     * @return self
     */
    public static function get($channel, array $data)
    {
        $book = Book::where('channel', $channel)->where('openid', $data['id'])->find();

        if (empty($book)) {
            $book = new Book([
                'channel' => $channel,
                'openid'  => $data['id'],
            ]);
        }

        $book->save([
            'name'     => $data['name'],
            'url'      => $data['url'],
            'git_url'  => $data['git_url'],
            'ssh_url'  => $data['ssh_url'] ?? null,
            'lfs_url'  => $data['lfs_url'] ?? null,
            'metadata' => $data['metadata'] ?? null,
        ]);

        return $book;
    }

    public function members()
    {
        return $this->belongsToMany(User::class, BookMember::class);
    }

    protected function getHashIdAttr()
    {
        return $this->invoke(function (Hashids $hashids) {
            return $hashids->encode($this->id);
        });
    }

    public function getArtifactPath()
    {
        $hash = sha1($this->id);
        return substr($hash, 0, 2) . '/' . substr($hash, 2, 2) . "/{$hash}";
    }

    public function getArtifact($type)
    {
        $dir = $this->getArtifactPath();

        $extensions = [
            'pdf'  => '.pdf',
            'epub' => '.epub',
            'word' => '.docx',
            'html' => '.tar.gz',
            'json' => '.tar.gz',
        ];

        return Path::join($dir, $type . $extensions[$type]);
    }

    protected function getHashPathAttr()
    {
        $hash = sha1($this->id);
        return substr($hash, 0, 2) . '/' . substr($hash, 2, 2) . "/{$hash}";
    }

    /**
     * @return Release|null
     */
    public function getLastRelease()
    {
        return $this->releases()->order('id desc')->find();
    }

    public function gc()
    {
        $releases = $this->releases()->order('id desc')->limit(5, PHP_INT_MAX)->whereNull('gc_time')->select();

        $releases->each(function (Release $release) {
            $release->transaction(function () use ($release) {
                $release->logs->delete();
                $release->isAutoWriteTimestamp(false)->save(['gc_time' => Date::now()]);
            });
        });
    }

    public function getRoom()
    {
        return "book_{$this->id}";
    }
}
