<?php

namespace app\model;

use app\lib\job\ReleaseJob;
use Carbon\Carbon;
use Exception;
use think\annotation\model\relation\BelongsTo;
use think\facade\Db;
use think\facade\Log;
use think\Model;
use think\swoole\Websocket;
use think\workflow\annotation\StateMachine;

/**
 * Class app\model\ReleaseLog
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $end_time
 * @property \Carbon\Carbon $start_time
 * @property int $id
 * @property int $release_id
 * @property int $space_id
 * @property int $status
 * @property string $trace
 * @property string $type
 * @property-read \app\model\Release $release
 * @property-read \app\model\Space $space
 * @property-read mixed $status_text
 * @method \think\model\relation\BelongsTo release()
 * @method bool canFailed()
 * @method bool canRetry()
 * @method bool canStart()
 * @method bool canSucceed()
 * @method void failed(array $context = [])
 * @method void retry(array $context = [])
 * @method void start(array $context = [])
 * @method void succeed(array $context = [])
 */
#[BelongsTo('release', Release::class)]
#[StateMachine(\app\workflow\ReleaseLog::class)]
class ReleaseLog extends Model
{
    protected $autoWriteTimestamp = false;

    protected $append = ['status_text'];

    protected $type = [
        'create_time' => Carbon::class,
        'start_time'  => Carbon::class,
        'end_time'    => Carbon::class,
    ];

    public static function onAfterInsert(self $model): void
    {
        $model->createJob();
        $model->refresh();
        $model->broadcast();
    }

    public static function onAfterRead(self $model): void
    {
        if (
            !$model->isCompleted() &&
            (
                (empty($model->start_time) && $model->create_time->lt(Carbon::now()->sub('1 hour'))) ||
                (!empty($model->start_time) && $model->start_time->lt(Carbon::now()->sub('1 hour')))
            )
        ) {
            $model->failed();
        }
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function isSucceed()
    {
        return $this->status == \app\workflow\ReleaseLog::STATUS_SUCCEED;
    }

    public function isRunning()
    {
        return $this->status == \app\workflow\ReleaseLog::STATUS_RUNNING;
    }

    public function isCompleted()
    {
        return in_array(
            $this->status,
            [
                \app\workflow\ReleaseLog::STATUS_FAILED,
                \app\workflow\ReleaseLog::STATUS_SUCCEED,
            ]
        );
    }

    public function broadcast()
    {
        $this->invoke(function (Websocket $websocket) {
            $room = $this->release->book->getRoom();
            $type = $this->getAttr('type');

            if ($type == $this->release->main) {
                $websocket->to($room)->emit('workspace.release', $this->status_text);
            }

            $websocket->to($room)->emit("release.{$this->release->id}.update", $type, $this);
        });
    }

    public function trace($message, $newline = true)
    {
        $this->invoke(function (Websocket $websocket) use ($newline, $message) {
            try {
                $message = trim($message);
                if ($newline) {
                    $message .= PHP_EOL;
                }

                $trace = $this->trace . $message;

                $this->save([
                    'trace' => Db::raw("concat(`trace`,'" . addslashes($message) . "')"),
                ]);

                $this->set('trace', $trace);

                $websocket->to($this->release->book->getRoom())
                    ->emit("release.{$this->release->id}.trace", $this->getAttr('type'), $message);
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        });
    }

    public function createJob()
    {
        ReleaseJob::create($this->release, $this->getAttr('type'), $this->release->timeout);
    }

    protected function getStatusTextAttr()
    {
        return
            [
                \app\workflow\ReleaseLog::STATUS_PENDING => 'pending',
                \app\workflow\ReleaseLog::STATUS_FAILED  => 'failed',
                \app\workflow\ReleaseLog::STATUS_SUCCEED => 'succeed',
                \app\workflow\ReleaseLog::STATUS_RUNNING => 'running',
            ]
            [$this->status ?? \app\workflow\ReleaseLog::STATUS_PENDING];
    }
}
