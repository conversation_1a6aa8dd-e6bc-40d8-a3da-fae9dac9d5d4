<?php

namespace app\model;

use Firebase\JWT\JWT;
use think\Model;

/**
 * Class app\model\Admin
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property string $avatar
 * @property string $channel
 * @property string $email
 * @property string $name
 * @property string $openid
 * @property string $password
 * @property-read mixed $token
 */
class Admin extends Model
{
    protected $hidden = ['password'];

    protected function getTokenAttr()
    {
        return JWT::encode([
            'exp' => time() + 60 * 60 * 24 * 3,
            'iat' => time(),
            'id'  => $this->id,
        ], config('app.token'), 'HS256');
    }
}
