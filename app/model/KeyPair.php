<?php

namespace app\model;

use phpseclib\Crypt\RSA;
use think\Model;

/**
 * Class KeyPair
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property int $book_id
 * @property int $id
 * @property int $key_id
 * @property int $user_id
 * @property string $private_key
 * @property string $public_key
 */
class KeyPair extends Model
{

    /**
     * @param User $user
     * @return KeyPair
     */
    public static function createWithRsa(User $user)
    {
        $rsa = new RSA();
        $rsa->setPublicKeyFormat(RSA::PUBLIC_FORMAT_OPENSSH);
        $result = $rsa->createKey();

        return self::create([
            'user_id'     => $user->id,
            'public_key'  => $result['publickey'],
            'private_key' => $result['privatekey'],
        ]);
    }

    /**
     * @param User $user
     * @param      $checker
     * @param      $creator
     * @return KeyPair
     */
    public static function getOrNew(User $user, $checker, $creator)
    {
        /** @var KeyPair $keyPair */
        $keyPair = self::where('user_id', $user->id)->find();

        if (!empty($keyPair) && $checker($keyPair)) {
            return $keyPair;
        }
        if ($keyPair) {
            $keyPair->delete();
        }

        $keyPair = self::createWithRsa($user);

        $creator($keyPair);
        return $keyPair;
    }
}
