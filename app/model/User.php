<?php

namespace app\model;

use app\lib\StaticData;
use think\helper\Str;
use think\Model;
use yunwuxin\social\User as SocialUser;

/**
 * Class User
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $tour_version
 * @property int $id
 * @property string $avatar
 * @property string $channel
 * @property string $email
 * @property string $name
 * @property string $openid
 * @property string $remember_token
 * @property string $token
 * @property-read \app\model\App $app
 * @property-read \app\model\Book[] $books
 */
class User extends Model
{
    use StaticData;

    protected $append = ['token', 'remember_token'];

    protected $type = [
        'tour_version' => 'json',
    ];

    /**
     * @param string $channel
     * @param array $data
     * @return self
     */
    public static function get($channel, array $data)
    {
        $user = User::where('channel', $channel)->where('openid', $data['id'])->find();

        if (empty($user)) {
            $user = new User([
                'channel' => $channel,
                'openid'  => $data['id'],
            ]);
        }

        $user->save([
            'name'   => $data['name'],
            'email'  => $data['email'] ?: '<EMAIL>',
            'avatar' => $data['avatar'],
            'token'  => $data['token'],
        ]);

        return $user;
    }

    public function books()
    {
        return $this->belongsToMany(Book::class, BookMember::class);
    }

    protected function getAppAttr()
    {
        $channel = $this->channel;

        //多空间模式
        if (Str::contains($channel, '@')) {
            [$channel,] = explode('@', $channel);
        }

        return App::where('name', $channel)->findOrFail();
    }

    /**
     * @return \app\lib\client\Driver
     */
    public function getClient()
    {
        return $this->getStaticData(function () {
            $class = "\\app\\lib\\client\\driver\\" . Str::studly($this->app->type);

            return new $class($this->app, $this);
        });
    }

    public function getRoom()
    {
        return "user_{$this->id}";
    }
}
