<?php

namespace app\model;

use app\lib\StaticData;
use think\annotation\model\relation\BelongsTo;
use think\annotation\model\relation\HasMany;
use think\Model;

/**
 * Class Release
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property bool $is_latest
 * @property int $book_id
 * @property int $id
 * @property int $timeout
 * @property int $user_id
 * @property string $main
 * @property string $message
 * @property string $sha
 * @property-read \app\model\Book $book
 * @property-read \app\model\ReleaseLog[]|\think\model\Collection $logs
 * @property-read \app\model\User $user
 * @property-read mixed $status
 * @property-read mixed $status_text
 * @property-read mixed $title
 * @method \think\model\relation\BelongsTo book()
 * @method \think\model\relation\HasMany logs()
 */
#[BelongsTo('book', Book::class)]
#[HasMany('logs', ReleaseLog::class)]
class Release extends Model
{
    use StaticData;

    protected $append = ['status', 'status_text', 'title'];

    public function user()
    {
        return $this->belongsTo(User::class)->withDefault(VirtualUser::createByOpenid($this->book->openid));
    }

    /**
     * @param      $type
     * @param bool $force
     * @return ReleaseLog
     */
    public function getLog($type, $force = true)
    {
        return $this->getStaticData(function () use ($force, $type) {
            return $this->logs()->where('type', $type)->failException($force)->find();
        });
    }

    public function isReleasing()
    {
        return in_array($this->status, [\app\workflow\ReleaseLog::STATUS_PENDING, \app\workflow\ReleaseLog::STATUS_RUNNING]);
    }

    protected function getIsLatestAttr()
    {
        $latest = Release::where('book_id', $this->book_id)->order('id desc')->find();
        return $latest->id == $this->id;
    }

    protected function getTitleAttr()
    {
        if (!empty($this->message)) {
            return strtok($this->message, "\n");
        }
        return '';
    }

    protected function getStatusAttr()
    {
        $log = $this->getLog($this->main, false);

        return $log ? $log->status : -2;
    }

    protected function getStatusTextAttr()
    {
        $log = $this->getLog($this->main, false);

        return $log ? $log->status_text : 'unknown';
    }
}
