<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\App
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property bool $proxy
 * @property int $id
 * @property int $login
 * @property string $client_id
 * @property string $client_secret
 * @property string $name
 * @property string $remark
 * @property string $title
 * @property string $token
 * @property string $type
 * @property string $url
 */
class App extends Model
{
    protected $type = [
        'expire_time' => Date::class,
    ];
}
