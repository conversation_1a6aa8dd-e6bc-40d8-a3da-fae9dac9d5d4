<?php

namespace app\job;

use app\model\Release;
use Carbon\Carbon;
use think\db\exception\ModelNotFoundException;
use think\queue\Job;

class MarkReleaseFailed
{
    public function fire(Job $job, $data)
    {
        [$id, $type] = $data;
        try {
            $release = Release::findOrFail($id);

            $log = $release->getLog($type);

            if ($log->isCompleted()) {
                $job->delete();
            } else {
                $end = Carbon::now()->sub($release->timeout, "seconds");

                $startTime = empty($log->start_time) ? $log->create_time : $log->start_time;

                if ($startTime->lt($end)) {
                    $log->trace("Timeout!");
                    $log->failed();
                    $job->delete();
                } else {
                    $job->release(10);
                }
            }
        } catch (ModelNotFoundException) {
            $job->delete();
        }
    }
}
