<?php

namespace app\job;

use app\model\Release;
use think\File;
use think\Filesystem;
use think\queue\Job;
use Throwable;

class ReleaseJob
{

    public function __construct(protected Filesystem $filesystem)
    {
    }

    public function fire(Job $job, $data)
    {
        if ($job->attempts() > 1) {
            $job->delete();
            $this->failed($data);
            return;
        }

        [$id, $type, $file] = $data;

        $file = new File($file);

        $release = Release::findOrFail($id);

        $log = $release->getLog($type);

        try {
            $book = $release->book;

            //保存文件
            $this->filesystem->putFileAs('artifact', $file, $book->getArtifact($type));

            $release->user->getClient()->release($release, $book, $type, $file);

            $log->trace('Upload succeed');

            $log->succeed();

            //gc
            if ($type == $release->main) {
                $release->book->gc();
            }

            $job->delete();
        } catch (Throwable $e) {
            $log->trace($e->getMessage());
            $log->failed();
        } finally {
            if (file_exists($file->getRealPath())) {
                unlink($file->getRealPath());
            }
        }
    }

    public function failed($data)
    {
        [$id, $type] = $data;

        $release = Release::findOrFail($id);

        $log = $release->getLog($type);

        $log->failed();
    }
}
