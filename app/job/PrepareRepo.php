<?php

namespace app\job;

use app\model\Space;
use Symfony\Component\Filesystem\Filesystem;
use think\facade\Log;
use think\helper\Arr;
use think\helper\Str;
use think\queue\Job;
use think\swoole\Websocket;
use topthink\git\exception\ReferenceNotFoundException;
use topthink\git\Repository;

class PrepareRepo
{
    public function __construct(protected Websocket $websocket, protected Filesystem $filesystem)
    {
    }

    public function fire(Job $job, $id)
    {
        $space = Space::findOrFail($id);
        try {
            $dir = $space->getRepoPath();
            if ($this->filesystem->exists($dir)) {
                $this->filesystem->remove($dir);
            }
            $this->filesystem->mkdir($dir);

            $remotePath = $space->getRemotePath();

            $environment = [];
            $sshCommand  = $space->getSshCommand();
            if ($sshCommand) {
                $environment['GIT_SSH_COMMAND'] = $sshCommand;
            }
            $lfs = $space->supportLfs();
            if ($lfs) {
                $environment['GIT_LFS_SKIP_SMUDGE'] = 1;
            }

            $callback = function ($buffer) use ($space) {
                $message = Arr::last(preg_split("/[\r\n]+/s", trim($buffer)));
                if (!Str::startsWith($message, 'Cloning into')) {
                    $this->websocket->to($space->getRoom())->emit('repo.cloning', $message);
                }
            };

            $repository = Repository::clone($dir, $remotePath, options: ['environment' => $environment, 'callback' => $callback]);

            if ($sshCommand) {
                $repository->run('config', ['core.sshCommand', $sshCommand]);
            }

            //lfs
            if ($lfs) {
                $repository->run('lfs', ['pull'], options: ['callback' => $callback]);
            }

            try {
                $repository->getHead();
            } catch (ReferenceNotFoundException) {
                //空仓库，初始化提交
                $repository->run('commit', ['--allow-empty', '-n', '-m', 'Initial commit']);
            }
            $space->save([
                'status' => Space::STATUS_READY,
            ]);
        } catch (\Throwable $e) {
            $space->save([
                'status' => Space::STATUS_FAILED,
            ]);
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
        }
        $this->websocket->to($space->getRoom())->emit('repo.cloned');
        $job->delete();
    }

    public function failed($id)
    {
        $space = Space::findOrFail($id);

        $space->save([
            'status' => Space::STATUS_FAILED,
        ]);
        $this->websocket->to($space->getRoom())->emit('repo.cloned');
    }
}
