<?php
// 应用公共文件

function asset($path)
{
    static $manifest = null;
    if (!isset($manifest) || env('APP_DEBUG')) {
        $manifestDirectory = root_path('asset/frontend/dist/asset');

        $manifestPath = $manifestDirectory . '/manifest.json';
        if (!is_file($manifestPath)) {
            throw new Exception('The manifest does not exist.');
        }

        $manifest = json_decode(file_get_contents($manifestPath), true);
    }

    if (!isset($manifest[$path])) {
        throw new Exception("Unable to locate file: {$path}.");
    }

    return $manifest[$path];
}

function get_package_version($name)
{
    try {
        $client = new \GuzzleHttp\Client([
            'base_uri' => 'https://npm.topthink.com',
        ]);

        $name = urlencode($name);

        $response = $client->get("/-/package/{$name}/dist-tags");

        $result  = json_decode((string) $response->getBody(), true);
        $version = env('APP_DEBUG') ? 'canary' : 'latest';
        return $result[$version];
    } catch (Exception) {
        return 'latest';
    }
}

