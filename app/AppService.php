<?php

namespace app;

use Hashids\Hashids;
use Symfony\Component\Process\Process;
use think\App;
use think\Config;
use think\Service;
use think\tracing\Tracer;
use topthink\git\Repository;

class AppService extends Service
{
    public function register()
    {
        $this->app->resolving(function ($instance, App $container) {
            if ($instance instanceof BaseController) {
                $container->invoke([$instance, 'initialize'], [], true);
            }
        });

        $this->app->bind(Hashids::class, function (Config $config) {
            return new Hashids($config->get('app.host'), 6);
        });
    }

    public function boot()
    {
        Repository::listen(function (Process $process) {
            $command       = $process->getCommandLine();
            $commands      = explode(' ', $command, 3);
            $operationName = trim($commands[0], "'") . '-' . trim($commands[1], "'");

            $span = app(Tracer::class)->startSpan($operationName, [
                'start_time' => (int) ($process->getStartTime() * 1000 * 1000),
                'tags'       => [
                    'args' => $commands[2] ?? '',
                ],
            ]);

            $span->finish();
        });
    }
}
