<?php

namespace app\repo\parser;

use topthink\git\parser\ParserBase;

class LfsFilesParser extends ParserBase
{

    protected function doParse()
    {
        $files = [];

        while (!$this->isFinished()) {
            $file = [];

            $file['id'] = $this->consumeLength(64);

            $this->consumeSpace();
            $this->consume('*');
            $this->consumeSpace();

            $file['path'] = $this->consumeTo("\n");

            $this->consumeNewLine();

            $files[] = $file;
        }

        return $files;
    }
}
