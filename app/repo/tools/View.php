<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class View extends FunctionCall
{
    protected string $name = 'view';

    protected string $description = '查看文件内容或目录列表。可以查看整个文件或指定行范围的内容。';

    protected array $parameters = [
        'type' => 'object',
        'properties' => [
            'path' => [
                'type' => 'string',
                'description' => '要查看的文件或目录路径'
            ],
            'view_range' => [
                'type' => 'array',
                'description' => '可选，指定要查看的行范围 [开始行, 结束行]，-1表示到文件末尾',
                'items' => [
                    'type' => 'integer'
                ],
                'minItems' => 2,
                'maxItems' => 2
            ]
        ],
        'required' => ['path']
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');
        $viewRange = $args->get('view_range');

        if (empty($path)) {
            return new Error('路径参数不能为空');
        }

        try {
            // 先尝试作为文件处理
            if ($this->workspace->hasFile($path)) {
                return $this->viewFile($path, $viewRange);
            } else {
                // 如果不是文件，尝试作为目录处理
                return $this->viewDirectory($path);
            }
        } catch (\Exception $e) {
            return new Error('查看文件失败: ' . $e->getMessage());
        }
    }

    private function viewDirectory($path)
    {
        try {
            $files = $this->workspace->readDir($path);

            $result = "目录内容: {$path}\n\n";

            foreach ($files as $file) {
                $type = $file['type'] === 'dir' ? '[目录]' : '[文件]';
                $result .= "{$type} {$file['filename']}\n";
            }

            return new Plain($result);
        } catch (\Exception $e) {
            return new Error('读取目录失败: ' . $e->getMessage());
        }
    }

    private function viewFile($path, $viewRange = null)
    {
        try {
            if (!$this->workspace->hasFile($path)) {
                return new Error("文件不存在: {$path}");
            }

            $content = $this->workspace->readFile($path);

            if ($content === null) {
                return new Error("无法读取文件: {$path}");
            }

            // 如果指定了行范围，则只返回指定范围的内容
            if ($viewRange && is_array($viewRange) && count($viewRange) === 2) {
                $lines = explode("\n", $content);
                $startLine = max(1, (int)$viewRange[0]) - 1; // 转换为0基索引
                $endLine = (int)$viewRange[1];

                if ($endLine === -1) {
                    $endLine = count($lines);
                } else {
                    $endLine = min($endLine, count($lines));
                }

                $selectedLines = array_slice($lines, $startLine, $endLine - $startLine);
                $content = implode("\n", $selectedLines);

                // 添加行号
                $numberedLines = [];
                foreach ($selectedLines as $index => $line) {
                    $lineNumber = $startLine + $index + 1;
                    $numberedLines[] = "{$lineNumber}: {$line}";
                }
                $content = implode("\n", $numberedLines);
            } else {
                // 为所有行添加行号
                $lines = explode("\n", $content);
                $numberedLines = [];
                foreach ($lines as $index => $line) {
                    $lineNumber = $index + 1;
                    $numberedLines[] = "{$lineNumber}: {$line}";
                }
                $content = implode("\n", $numberedLines);
            }

            return new Plain($content);
        } catch (\Exception $e) {
            return new Error('读取文件失败: ' . $e->getMessage());
        }
    }
}
