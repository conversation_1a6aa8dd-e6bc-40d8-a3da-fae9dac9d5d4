<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class StrReplace extends FunctionCall
{
    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path   = $args->get('path');
        $oldStr = $args->get('old_str');
        $newStr = $args->get('new_str');

        try {
            // 检查文件是否存在
            if (!$this->workspace->hasFile($path)) {
                return new Error("文件不存在: {$path}");
            }

            // 读取文件内容
            $content = $this->workspace->readFile($path);
            if ($content === null) {
                return new Error("无法读取文件: {$path}");
            }

            // 检查要替换的文本是否存在
            $matchCount = substr_count($content, $oldStr);

            if ($matchCount === 0) {
                return new Error("未找到要替换的文本");
            }

            if ($matchCount > 1) {
                return new Error("找到 {$matchCount} 个匹配项，请提供更具体的文本以确保唯一匹配");
            }

            // 执行替换
            $newContent = str_replace($oldStr, $newStr, $content);

            // 写入文件
            $this->workspace->writeFile($path, $newContent);

            return new Plain("成功在 {$path} 中替换了 1 处文本");
        } catch (\Exception $e) {
            return new Error('替换文本失败: ' . $e->getMessage());
        }
    }
}
