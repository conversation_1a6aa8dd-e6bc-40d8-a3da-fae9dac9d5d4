<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class Create extends FunctionCall
{
    protected string $name = 'create';

    protected string $description = '创建新文件并写入指定内容。如果文件已存在则会报错。';

    protected array $parameters = [
        'type' => 'object',
        'properties' => [
            'path' => [
                'type' => 'string',
                'description' => '要创建的文件路径'
            ],
            'file_text' => [
                'type' => 'string',
                'description' => '要写入文件的内容',
                'default' => ''
            ]
        ],
        'required' => ['path']
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');
        $fileText = $args->get('file_text', '');

        if (empty($path)) {
            return new Error('路径参数不能为空');
        }

        try {
            // 检查文件是否已存在
            if ($this->workspace->hasFile($path)) {
                return new Error("文件已存在: {$path}");
            }

            // 创建文件
            $this->workspace->writeFile($path, $fileText);

            // 验证文件是否创建成功
            if (!$this->workspace->hasFile($path)) {
                return new Error("文件创建失败: {$path}");
            }

            $lineCount = substr_count($fileText, "\n") + 1;
            return new Plain("文件创建成功: {$path} ({$lineCount} 行)");
        } catch (\Exception $e) {
            return new Error('创建文件失败: ' . $e->getMessage());
        }
    }
}
