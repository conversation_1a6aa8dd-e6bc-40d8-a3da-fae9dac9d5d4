<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class Create extends FunctionCall
{
    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path     = $args->get('path');
        $fileText = $args->get('file_text', '');

        try {
            // 检查文件是否已存在
            if ($this->workspace->hasFile($path)) {
                return new Error("文件已存在: {$path}");
            }

            // 创建文件
            $this->workspace->writeFile($path, $fileText);

            // 验证文件是否创建成功
            if (!$this->workspace->hasFile($path)) {
                return new Error("文件创建失败: {$path}");
            }

            $lineCount = substr_count($fileText, "\n") + 1;
            return new Plain("文件创建成功: {$path} ({$lineCount} 行)");
        } catch (\Exception $e) {
            return new Error('创建文件失败: ' . $e->getMessage());
        }
    }
}
