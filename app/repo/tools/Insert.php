<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Plain;
use think\agent\tool\result\Error;

class Insert extends FunctionCall
{
    protected string $name = 'insert';

    protected string $description = '在文件的指定位置插入新文本。可以在文件开头或指定行后插入内容。';

    protected array $parameters = [
        'type' => 'object',
        'properties' => [
            'path' => [
                'type' => 'string',
                'description' => '要修改的文件路径'
            ],
            'insert_line' => [
                'type' => 'integer',
                'description' => '插入位置的行号，0表示在文件开头插入，其他数字表示在该行后插入',
                'minimum' => 0
            ],
            'new_str' => [
                'type' => 'string',
                'description' => '要插入的文本内容'
            ]
        ],
        'required' => ['path', 'insert_line', 'new_str']
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path       = $args->get('path');
        $insertLine = $args->get('insert_line');
        $newStr     = $args->get('new_str');

        try {
            // 检查文件是否存在
            if (!$this->workspace->hasFile($path)) {
                return new Error("文件不存在: {$path}");
            }

            // 读取文件内容
            $content = $this->workspace->readFile($path);
            if ($content === null) {
                return new Error("无法读取文件: {$path}");
            }

            $lines      = explode("\n", $content);
            $insertLine = (int) $insertLine;

            // 验证插入位置
            if ($insertLine < 0 || $insertLine > count($lines)) {
                return new Error("插入位置无效: {$insertLine}，文件共有 " . count($lines) . " 行");
            }

            // 在指定位置插入新内容
            if ($insertLine === 0) {
                // 在文件开头插入
                array_unshift($lines, $newStr);
            } else {
                // 在指定行后插入
                array_splice($lines, $insertLine, 0, [$newStr]);
            }

            // 重新组合内容
            $newContent = implode("\n", $lines);

            // 写入文件
            $this->workspace->writeFile($path, $newContent);

            $insertedLines = substr_count($newStr, "\n") + 1;
            return new Plain("成功在 {$path} 的第 {$insertLine} 行后插入了 {$insertedLines} 行内容");
        } catch (\Exception $e) {
            return new Error('插入内容失败: ' . $e->getMessage());
        }
    }
}
