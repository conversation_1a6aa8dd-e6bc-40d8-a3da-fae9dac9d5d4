<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2017 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\repo\message;

use app\repo\Message;

class File extends Message
{
    public function read($filename, $hash)
    {
        $content = $this->workspace->readFile($filename) ?? '';
        $content = base64_encode($content);
        if (!empty($hash) && sha1($content) === $hash) {
            return true;
        }

        return $content;
    }

    public function write($filename, $content)
    {
        if (empty($content)) {
            $this->workspace->removeFile($filename);
        } else {
            $content = base64_decode($content);
            $this->workspace->writeFile($filename, $content);
        }
    }

    public function rename($oldPath, $newPath)
    {
        $this->workspace->renameFile($oldPath, $newPath);
    }

    public function remove($filename)
    {
        $this->workspace->removeFile($filename);
    }

    public function readDir($root = ''): array
    {
        return $this->workspace->readDir($root);
    }

    public function search($query)
    {
        return $this->workspace->searchFile($query, true);
    }

    public function replace($filename, $replace, $line, $offset, $length)
    {
        $this->workspace->replaceFile($filename, $replace, $line, $offset, $length);
    }

}
