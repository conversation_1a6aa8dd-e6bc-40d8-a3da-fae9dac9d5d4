<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2017 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\repo\message;

use app\repo\Message;

class History extends Message
{

    public function read($path, $offset)
    {
        return $this->workspace->readHistory($path, $offset);
    }

}
