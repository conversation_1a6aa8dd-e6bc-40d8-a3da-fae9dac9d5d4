<?php

namespace app\repo;

use app\model\Space;
use app\repo\exception\WorkspaceException;
use OpenTracing\Span;
use think\App;
use think\swoole\Websocket;

class Client
{
    protected ?Workspace $workspace = null;
    protected ?Span      $span      = null;

    public function __construct(protected App $app, protected Websocket $websocket, protected Space $space)
    {
        $this->websocket->to($space->getRoom())->emit('abort', '该账号已在其他地方登陆');
        //加入文档小组
        $this->websocket->join($space->book->getRoom(), $space->user->getRoom(), $space->getRoom());

        $this->workspace = $this->createWorkspace();
    }

    public function getWorkspace($create = false)
    {
        if ($this->workspace) {
            return $this->workspace;
        }
        if ($create) {
            return $this->workspace = $this->createWorkspace(true);
        }
        throw new WorkspaceException('Workspace has not been initialized');
    }

    protected function createWorkspace($force = false)
    {
        try {
            $workspace = $this->space->createWorkspace();

            //监控文件
            $workspace->watchFiles();

            $workspace->on('*', function ($event, ...$args) {
                $this->websocket->emit($event, ...$args);
            });

            return $workspace;
        } catch (\Throwable $e) {
            if ($force) {
                throw $e;
            }
            return null;
        }
    }

    public function close()
    {
        $this->workspace?->close();
    }

    public function getPath()
    {
        return "{$this->space->user->id}/{$this->space->book->id}";
    }

    public function getSpace()
    {
        return $this->space;
    }

    public function setSpan(?Span $span): void
    {
        $this->span = $span;
    }

    public function getSpan()
    {
        return $this->span;
    }
}
