<?php

namespace app\repo;

use ArrayAccess;
use ArrayObject;
use JsonSerializable;
use think\helper\Arr;

class Config implements JsonSerializable, ArrayAccess
{
    const FILE = 'book.json';

    protected $values = [];

    public function getStructure($type, $default)
    {
        return $this->getValue('structure.' . $type, $default);
    }

    public function setValues($values)
    {
        $this->values = $values;
    }

    public function getValue($offset, $default = null)
    {
        return Arr::get($this->values, $offset, $default);
    }

    public function getValues()
    {
        return $this->values;
    }

    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
        return new ArrayObject($this->values);
    }

    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return array_key_exists($offset, $this->values);
    }

    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->values[$offset];
    }

    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        $this->values[$offset] = $value;
    }

    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->values[$offset]);
    }
}
