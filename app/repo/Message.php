<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\repo;

use app\model\Book;
use app\model\Space;
use app\model\User;
use think\swoole\Websocket;
use topthink\git\Repository;

/**
 * @property Space $space
 * @property Book $book
 * @property User $user
 * @property Workspace $workspace
 * @property Repository $repo
 */
abstract class Message
{
    public function __construct(protected Client $client, protected Websocket $websocket)
    {
    }

    public function __get(string $name)
    {
        return match ($name) {
            'space' => $this->client->getSpace(),
            'book' => $this->space->book,
            'user' => $this->space->user,
            'workspace' => $this->client->getWorkspace(),
            'repo' => $this->workspace->getRepo(),
        };
    }
}
