<?php

namespace app\rpc;

use app\job\ReleaseJob;
use app\model\ReleaseLog;
use ArrayObject;
use think\facade\Log;
use think\facade\Queue;
use think\swoole\concerns\WithMiddleware;
use think\swoole\Lock;
use think\swoole\rpc\File;
use think\swoole\rpc\Protocol;

class Release
{
    use WithMiddleware;

    /**
     * @var \app\model\Release
     */
    protected $release;

    /**
     * @var ReleaseLog
     */
    protected $log;

    public function __construct(protected Lock $lock)
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();

            $this->release = \app\model\Release::findOrFail($context['id']);
            $this->log     = $this->release->getLog($context['type']);

            if (!$this->log->isCompleted()) {
                return $next($protocol);
            }
        });
    }

    public function getInfo()
    {
        $release = $this->release;
        $space   = $this->log->space;

        return [
            'sha'     => $release->sha,
            'options' => [
                'id'        => $release->book->hash_id,
                'metadata'  => new ArrayObject($this->space->book->metadata ?? []),
                'plugins'   => [
                    'host'   => config('app.plugins_host'),
                    'preset' => $space->getPresetPlugins(),
                ],
                'lfs'       => $space->book->lfs_url,
                'poweredBy' => $space->getPoweredBy(),
                'asset'     => $space->getAssetType(),
            ],
        ];
    }

    public function getResource(): \think\File
    {
        $release = $this->release;
        $space   = $this->log->space;

        $repo = $space->getRepo();

        $path = sys_get_temp_dir() . '/' . uniqid();

        $tag = "release.{$release->id}";

        try {
            $this->lock->lock($tag);
            $repo->run('tag', ['-f', $tag, $release->sha]);
            $repo->run('bundle', ['create', $path, $tag]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            throw $e;
        } finally {
            $this->lock->unlock($tag);
        }

        return new File($path);
    }

    public function start()
    {
        $this->log->start();
    }

    public function trace($message)
    {
        $this->log->trace($message);
    }

    public function failed()
    {
        $this->log->failed();
    }

    public function succeed(\think\File $file = null)
    {
        if ($file) {
            $target = $file->move(sys_get_temp_dir() . '/book');
            $this->log->trace('...');

            Queue::push(ReleaseJob::class, [$this->release->id, $this->log->type, $target->getRealPath()]);
        } else {
            $this->log->succeed();
        }
    }
}
