<?php

namespace app\rpc;

use PharData;
use think\Cache;
use think\swoole\concerns\WithMiddleware;
use think\swoole\rpc\File;
use think\swoole\rpc\Protocol;
use think\swoole\Websocket;

class Import
{
    use WithMiddleware;

    protected $import;

    public function __construct(protected Websocket $websocket, protected Cache $cache)
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();

            $this->import = $this->cache->get("import#{$context['id']}");

            if ($this->import) {
                return $next($protocol);
            }
        });
    }

    public function getMetadata()
    {
        return $this->import['metadata'];
    }

    public function getFile($path): \think\File
    {
        return new File(runtime_path('temp/import') . $path);
    }

    public function trace(string $message)
    {
        $this->websocket->to($this->import['client'])
            ->emit("import.{$this->import['id']}", [
                'type'    => 'trace',
                'message' => $message,
            ]);
    }

    public function failed(string $error)
    {
        $this->websocket->to($this->import['client'])
            ->emit("import.{$this->import['id']}", [
                'type'  => 'error',
                'error' => $error,
            ]);
    }

    public function succeed($summary, \think\File $file)
    {
        $filename = $file->getRealPath() . '.tar.gz';
        //重命名
        rename($file->getRealPath(), $filename);

        try {
            //解压文件
            $p = new PharData($filename);
            $p->extractTo($this->import['dest'], overwrite: true);

            $this->websocket->to($this->import['client'])
                ->emit("import.{$this->import['id']}", [
                    'type'   => 'result',
                    'result' => $summary,
                ]);
        } finally {
            unlink($filename);
        }
    }
}
