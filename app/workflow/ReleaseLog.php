<?php

namespace app\workflow;

use Carbon\Carbon;
use think\workflow\StateMachine;

class ReleaseLog extends StateMachine
{
    const STATUS_PENDING = 0;
    const STATUS_FAILED  = -1;
    const STATUS_SUCCEED = 1;
    const STATUS_RUNNING = 2;

    public $name = 'status';

    public $initial = self::STATUS_PENDING;

    public $places = [
        self::STATUS_PENDING,
        self::STATUS_FAILED,
        self::STATUS_SUCCEED,
        self::STATUS_RUNNING,
    ];

    public $transitions = [
        'failed'  => [[self::STATUS_PENDING, self::STATUS_RUNNING], self::STATUS_FAILED],
        'succeed' => [self::STATUS_RUNNING, self::STATUS_SUCCEED],
        'start'   => [self::STATUS_PENDING, self::STATUS_RUNNING],
        'retry'   => [[self::STATUS_FAILED, self::STATUS_SUCCEED], self::STATUS_PENDING],
    ];

    public function after($event, \app\model\ReleaseLog $model)
    {
        $model->broadcast();
    }

    public function beforeRetry(\app\model\ReleaseLog $model)
    {
        $model->create_time = Carbon::now();
        $model->start_time  = null;
        $model->end_time    = null;
        $model->trace       = '';
    }

    public function afterRetry(\app\model\ReleaseLog $model)
    {
        $model->createJob();
    }

    public function beforeStart(\app\model\ReleaseLog $model)
    {
        $model->trace      = '';
        $model->start_time = Carbon::now();
    }

    public function beforeSucceed(\app\model\ReleaseLog $model)
    {
        $model->end_time = Carbon::now();
    }

    public function beforeFailed(\app\model\ReleaseLog $model)
    {
        $model->end_time = Carbon::now();
    }
}
