<?php

namespace app\lib;

use Symfony\Component\Process\Process;

class Ripgrep
{

    public function __construct(protected string $root)
    {
    }

    protected function getArgs(string $query, $isCaseSensitive = false, $isWordMatch = false, $isRegExp = false)
    {
        $args   = ['--hidden', '--no-require-git'];
        $args[] = $isCaseSensitive ? '--case-sensitive' : '--ignore-case';
        $args[] = '--no-ignore';

        $args[] = '--encoding';
        $args[] = 'utf-8';

        if ($query == '--') {
            $isRegExp = true;
            $query    = '\\-\\-';
        }

        $args[] = '--crlf';
        if ($isWordMatch) {

        } elseif ($isRegExp) {
            $args[] = '--regexp';
            $args[] = $query;
        } else {
            $pattern = $query;
            $args[]  = '--fixed-strings';
        }

        $args[] = '--type';
        $args[] = 'markdown';

        $args[] = '-g';
        $args[] = '!SUMMARY.md';

        $args[] = '--json';
        $args[] = '--';

        if (!empty($pattern)) {
            $args[] = $pattern;
        }

        $args[] = '.';

        return $args;
    }

    public function search(string $query, $isCaseSensitive = false, $isWordMatch = false, $isRegExp = false)
    {
        $args = $this->getArgs($query, $isCaseSensitive, $isWordMatch, $isRegExp);

        array_unshift($args, 'rg');

        $process = new Process($args, $this->root);

        $process->run();

        $results = [];

        if ($process->isSuccessful()) {
            $lines = explode("\n", trim($process->getOutput()));
            foreach ($lines as $line) {
                $parsedLine = json_decode($line, true);
                if ($parsedLine['type'] == 'match') {
                    $data = $parsedLine['data'];

                    $matchPath = ltrim($data['path']['text'], './');

                    if (!isset($results[$matchPath])) {
                        $results[$matchPath] = [
                            'path'    => $matchPath,
                            'matches' => [],
                        ];
                    }

                    $results[$matchPath]['matches'][] = $this->createSearchMatch($data);
                }
            }
        }

        return array_values($results);
    }

    protected function createSearchMatch($data)
    {
        $fullText   = $data['lines']['text'];
        $lineNumber = $data['line_number'];

        if (empty($data['submatches'])) {
            $data['submatches'][] = $fullText ? [
                'start' => 0,
                'end'   => 1,
                'match' => [
                    'text' => $fullText,
                ],
            ] : [
                'start' => 0,
                'end'   => 0,
                'match' => [
                    'text' => '',
                ],
            ];
        }

        $ranges = array_filter(array_map(function ($match) use (&$prevMatchEndLine, &$prevMatchEndCol, &$prevMatchEnd, $fullText) {
            return [
                'start' => $match['start'],
                'end'   => $match['end'],
            ];
        }, $data['submatches']));

        return [
            'text'       => $fullText,
            'lineNumber' => $lineNumber,
            'ranges'     => $ranges,
        ];
    }
}
