<?php

namespace app\lib;

use think\helper\Str;

trait StaticData
{
    protected $staticData = [];

    protected function getStaticData($name, callable $callback = null)
    {
        if (func_num_args() == 1) {
            $callback = $name;
            $trace    = debug_backtrace(false, 2);
            $function = Str::snake($trace[1]['function']);
            $args     = $trace[1]['args'];
            $name     = $function . serialize($args);
        }
        if (!array_key_exists($name, $this->staticData)) {
            $this->staticData[$name] = $callback();
        }

        return $this->staticData[$name];
    }
}
