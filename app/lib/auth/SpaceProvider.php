<?php

namespace app\lib\auth;

use app\model\Space;
use yunwuxin\auth\interfaces\StatefulProvider;

class SpaceProvider implements StatefulProvider
{
    public function retrieveByCredentials($credentials)
    {
    }

    public function getId($space)
    {
        return "space@{$space->id}";
    }

    public function getRememberToken($user)
    {
    }

    public function setRememberToken($user, $token)
    {
    }

    public function retrieveById($id)
    {
        if (str_contains($id, '@')) {
            [$type, $id] = explode('@', $id, 2);
            if ($type === 'space') {
                return Space::find($id);
            }
        }
    }

    public function retrieveByToken($id, $token)
    {

    }
}
