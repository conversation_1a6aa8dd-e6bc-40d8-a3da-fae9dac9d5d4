<?php

namespace app\lib\auth;

use app\model\User;
use yunwuxin\auth\interfaces\StatefulProvider;

class UserProvider implements StatefulProvider
{

    public function retrieveByCredentials($credentials)
    {
    }

    public function getId($user)
    {
        return "user@{$user->id}";
    }

    public function getRememberToken($user)
    {
    }

    public function setRememberToken($user, $token)
    {
    }

    public function retrieveById($id)
    {
        if (str_contains($id, '@')) {
            [$type, $id] = explode('@', $id, 2);
            if ($type === 'user') {
                return User::find($id);
            }
        }
    }

    public function retrieveByToken($id, $token)
    {

    }
}
