<?php

namespace app\lib\social;

use GuzzleHttp\Exception\RequestException;
use think\App;
use think\Request;
use yunwuxin\social\AccessToken;
use yunwuxin\social\Channel;
use yunwuxin\social\exception\Exception;
use yunwuxin\social\User;

class <PERSON>itee extends Channel
{
    protected $host = 'https://gitee.com';

    public function __construct(App $app, Request $request, $name, $config)
    {
        parent::__construct($app, $request, $name, $config);
        if (!empty($config['host'])) {
            $this->host = $config['host'];
        }
    }

    public function getAuthUrl()
    {
        return $this->buildAuthUrlFromBase("{$this->host}/oauth/authorize");
    }

    protected function getTokenUrl()
    {
        return "{$this->host}/oauth/token";
    }

    protected function getTokenParams($code)
    {
        return parent::getTokenParams($code) + ['grant_type' => 'authorization_code'];
    }

    protected function getUserByToken(AccessToken $token)
    {
        $userUrl  = "{$this->host}/api/v5/user";
        $response = $this->getHttpClient()->get($userUrl, [
                'query' => [
                    'access_token' => $token->getValue(),
                ],
            ]
        );
        return json_decode($response->getBody(), true);
    }

    protected function makeUser(array $user)
    {
        return User::make($user, [
            'nickname' => 'name',
            'avatar'   => 'avatar_url',
        ]);
    }

    protected function getAccessToken($code)
    {
        try {
            $response = $this->getHttpClient()->post($this->getTokenUrl(), [
                'query' => $this->getTokenParams($code),
            ]);
        } catch (RequestException $exception) {
            if ($exception->hasResponse()) {
                $response = $exception->getResponse();
            } else {
                throw $exception;
            }
        }

        $body = json_decode($response->getBody(), true);

        if (isset($body['access_token'])) {
            return AccessToken::make($body);
        }

        throw new Exception($body['error_description']);
    }
}
