<?php

namespace app\lib\social;

use think\App;
use think\Request;
use yunwuxin\social\AccessToken;
use yunwuxin\social\Channel;
use yunwuxin\social\exception\Exception;
use yunwuxin\social\User;

class Gitlab extends Channel
{
    protected $host = "https://gitlab.com";

    public function __construct(App $app, Request $request, $name, $config)
    {
        parent::__construct($app, $request, $name, $config);
        if (!empty($config['host'])) {
            $this->host = $config['host'];
        }
    }

    public function getAuthUrl()
    {
        return $this->buildAuthUrlFromBase("{$this->host}/oauth/authorize");
    }

    protected function getTokenUrl()
    {
        return "{$this->host}/oauth/token";
    }

    protected function getUserByToken(AccessToken $token)
    {
        $userUrl  = "{$this->host}/api/v4/user?access_token={$token}";
        $response = $this->getHttpClient()->get($userUrl);
        return json_decode($response->getBody(), true);
    }

    /**
     * 创建User对象
     * @param array $user
     * @return User
     */
    protected function makeUser(array $user)
    {
        return User::make($user, [
            'nickname' => 'name',
            'avatar'   => 'avatar_url',
        ]);
    }

    protected function getTokenParams($code)
    {
        return parent::getTokenParams($code) + ['grant_type' => 'authorization_code'];
    }

    protected function getAccessToken($code)
    {
        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'headers'     => ['Accept' => 'application/json'],
            'form_params' => $this->getTokenParams($code),
        ]);

        $body = json_decode($response->getBody(), true);

        if (isset($body['access_token'])) {
            return AccessToken::make($body);
        }

        throw new Exception($body['error_description']);
    }
}
