<?php

namespace app\lib;

use think\App;
use TopThinkCloud\Client;

class Cloud
{
    protected ?Client $client;

    public function __construct(protected App $app)
    {

    }

    public function getAuthorizeUrl()
    {
        return $this->getClient(false)->getAuth()->getAuthorizeUrl();
    }

    public function getUserInfo($token)
    {
        return $this->getClient($token)->currentUser()->info();
    }

    public function isAvailable()
    {
        return $this->app->bound(Client::class);
    }

    protected function getClient($token = null)
    {
        if (!$this->isAvailable()) {
            throw new \RuntimeException('cloud is not available');
        }

        $client = $this->app->make(Client::class);

        if ($token !== false) {
            $client->authenticate($token);
        }
        return $client;
    }
}
