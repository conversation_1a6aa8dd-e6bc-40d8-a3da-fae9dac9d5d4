<?php

namespace app\lib;

use app\lib\social\Gitee;
use app\lib\social\Github;
use app\lib\social\Gitlab;
use app\model\App;
use InvalidArgumentException;
use think\helper\Arr;

class Social extends \yunwuxin\Social
{
    /**
     * 获取驱动配置
     * @param string $channel
     * @param ?string $name
     * @param null $default
     * @return mixed
     */
    public function getChannelConfig(string $channel, string $name = null, $default = null)
    {
        $config = $this->getDbConfig($channel);

        if (!empty($config)) {
            return Arr::get($config, $name, $default);
        }

        throw new InvalidArgumentException("Channel [$channel] not found.");
    }

    protected function getDbConfig($channel)
    {
        /** @var App $app */
        $app = App::where('name', $channel)->find();

        $types = [
            'gitlab' => Gitlab::class,
            'gitee'  => Gitee::class,
            'github' => Github::class,
        ];

        if ($app && isset($types[$app->type]) && (!$app->expire_time || Date::now()->lt($app->expire_time))) {
            $config = [
                'type'          => $types[$app->type],
                'client_id'     => $app->client_id,
                'client_secret' => $app->client_secret,
                'host'          => $app->url,
            ];
            $proxy  = env('PROXY');
            if ($app->proxy && $proxy) {
                $config['client_config'] = [
                    'proxy' => $proxy,
                ];
            }

            return $config;
        }

        return null;
    }
}
