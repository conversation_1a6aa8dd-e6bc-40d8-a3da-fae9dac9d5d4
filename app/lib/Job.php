<?php

namespace app\lib;

use K8s\Api\Model\Api\Core\v1\ConfigMap;
use K8s\Api\Model\Api\Core\v1\Container;
use K8s\Api\Model\Api\Core\v1\EnvVar;
use K8s\Api\Model\Api\Core\v1\PersistentVolumeClaimVolumeSource;
use K8s\Api\Model\Api\Core\v1\PodTemplateSpec;
use K8s\Api\Model\Api\Core\v1\ResourceRequirements;
use K8s\Api\Model\Api\Core\v1\Volume;
use K8s\Api\Model\Api\Core\v1\VolumeMount;
use K8s\Client\Exception\KubernetesException;
use K8s\Client\Http\Exception\HttpException;
use K8s\Client\K8s;
use K8s\Client\Options;
use K8s\HttpGuzzle\ClientFactory;

abstract class Job
{
    protected static $token     = null;
    protected static $namespace = null;

    protected static function supportK8sJob()
    {
        return file_exists('/var/run/secrets/kubernetes.io/serviceaccount');
    }

    private static function getToken()
    {
        if (is_null(self::$token)) {
            self::$token = file_get_contents('/var/run/secrets/kubernetes.io/serviceaccount/token');
        }
        return self::$token;
    }

    private static function getNamespace()
    {
        if (is_null(self::$namespace)) {
            self::$namespace = @file_get_contents('/var/run/secrets/kubernetes.io/serviceaccount/namespace');
        }
        return self::$namespace;
    }

    protected static function createK8sJob($name, $args, $labels = [], $timeout = 300)
    {
        $options = new Options('https://kubernetes.default.svc');

        $options->setToken(self::getToken());

        if ($namespace = self::getNamespace()) {
            $options->setNamespace($namespace);
        }

        $clientFactory = new ClientFactory([
            'verify' => '/var/run/secrets/kubernetes.io/serviceaccount/ca.crt',
        ]);

        $options->setHttpClientFactory($clientFactory);

        $k8s = new K8s($options);

        $config = $k8s->read('release-config', ConfigMap::class)->getData() ?? [];

        $imageName = $config['image-name'] ?? "registry-vpc.cn-shanghai.aliyuncs.com/topthink/write-release";
        $imageTag  = $config['image-tag'] ?? 'master';

        $container = new Container('release', "{$imageName}:{$imageTag}");
        $container->setImagePullPolicy('IfNotPresent');
        $template = new PodTemplateSpec(null, [$container]);

        if (!env('APP_DEBUG', false)) {
            $resources = new ResourceRequirements();
            $resources->setRequests([
                'cpu'    => 0.5,
                'memory' => '1Gi',
            ]);
            $container->setResources($resources);
        }

        try {
            $xHost = $k8s->read('common-config', ConfigMap::class)->getData()['x-host'];

            $container->addEnv((new EnvVar('PHP_X_HOST'))->setValue($xHost));

            //挂载npm目录
            $volume = new Volume('npm');
            $volume->setPersistentVolumeClaim(new PersistentVolumeClaimVolumeSource('release'));
            $template->addVolumes($volume);
            $volumeMount = new VolumeMount('/root/npm-global', $volume->getName());
            $container->addVolumeMounts($volumeMount);
        } catch (HttpException|KubernetesException) {
            //TODO 本地环境
            //挂载app目录
            $volume = new Volume('app');
            $volume->setPersistentVolumeClaim(new PersistentVolumeClaimVolumeSource('write-pvc'));
            $template->addVolumes($volume);
            $volumeMount = new VolumeMount('/opt/htdocs', $volume->getName());
            $volumeMount->setSubPath('./release');
            $container->addVolumeMounts($volumeMount);

            //挂载npm目录
            $volume = new Volume('npm');
            $volume->setPersistentVolumeClaim(new PersistentVolumeClaimVolumeSource('write-data-pvc'));
            $template->addVolumes($volume);
            $volumeMount = new VolumeMount('/root/npm-global', $volume->getName());
            $volumeMount->setSubPath('./npm');
            $container->addVolumeMounts($volumeMount);
        }

        $template->setLabels($labels);

        $container->setCommand(['php']);
        $container->setArgs($args);

        $template->setRestartPolicy('Never');
        $template->setActiveDeadlineSeconds($timeout);

        $job = new \K8s\Api\Model\Api\Batch\v1\Job(null, $template);

        $job->setTtlSecondsAfterFinished(300);
        $job->setGenerateName($name);
        $job->setBackoffLimit(0);

        $k8s->create($job);
    }
}
