<?php

namespace app\lib\client;

use app\model\Book;
use app\model\Release;
use Symfony\Component\Filesystem\Filesystem;
use topthink\git\Repository;
use RuntimeException;
use Symfony\Component\Process\Process;
use think\File;

trait PushToPages
{
    protected function pushToPages(Release $release, Book $book, $type, File $file, $extra = [])
    {
        $filesystem = new Filesystem();

        $path = runtime_path('book/' . uniqid());
        $filesystem->mkdir($path);

        $log = $release->getLog($type);

        try {
            $log->trace('Init gh-pages repo');

            $repository = Repository::init($path, false, ['-b', 'gh-pages']);
            $repository->getRemote()->add('origin', $this->getRemotePath($book));
            //config
            $sshCommand = $this->getSshCommand($book);
            if ($sshCommand) {
                $repository->run('config', ['core.sshCommand', $sshCommand]);
            }

            $log->trace('.');
            $log->trace('Extracting book');
            $process = Process::fromShellCommandline("tar -xzvf {$file->getRealPath()} -C {$path}");

            if ($process->run() != 0) {
                throw new RuntimeException('Extract source failed:' . $process->getErrorOutput());
            }

            foreach ($extra as $file => $content) {
                $filesystem->dumpFile($path . '/' . $file, $content);
            }

            $log->trace('Add files');
            $repository->add();
            if ($repository->isDirty()) {
                $repository->run('commit', ['-m', 'release']);
            }
            $log->trace('Site built, push now');

            $repository->push('gh-pages', force: true, options: ['callback' => function () use ($log) {
                $log->trace('.', false);
            }]);

            $log->trace('.');

            $log->trace('push finished');
        } finally {
            $filesystem->remove($path);
        }
    }
}
