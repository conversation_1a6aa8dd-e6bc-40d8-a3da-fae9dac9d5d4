<?php

namespace app\lib\client;

use GuzzleHttp\Psr7\Response;
use RuntimeException;

class ClientProxy
{
    public function __construct(protected $client)
    {
    }

    public function __call($name, $args)
    {
        $response = $this->client->{$name}(...$args);
        if ($response instanceof Response) {
            if (str_starts_with($response->getHeaderLine('Content-Type'), 'application/json')) {
                $body    = $response->getBody()->__toString();
                $content = json_decode($body, true);
                if (JSON_ERROR_NONE !== json_last_error()) {
                    throw new RuntimeException(json_last_error_msg());
                }
                return $content;
            }
        }
        return $response;
    }
}
