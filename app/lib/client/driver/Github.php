<?php

namespace app\lib\client\driver;

use app\lib\client\Driver;
use app\lib\client\InteractsWithSocial;
use app\lib\client\PushToPages;
use app\model\Book;
use app\model\BookMember;
use app\model\KeyPair;
use app\model\Release;
use Exception;
use Github\Client;
use Github\Exception\RuntimeException;
use Github\HttpClient\Builder;
use think\File;
use think\helper\Arr;
use yunwuxin\auth\exception\AuthorizationException;

/**
 * Class Github
 * @package app\lib\client
 * @property Client $client
 */
class Github extends Driver
{
    use InteractsWithSocial, PushToPages;

    public function getBookByUrl($url)
    {
        [$owner, $repo] = $this->parseUrl($url);

        try {
            $data = $this->client->repo()->show($owner, $repo);
        } catch (RuntimeException $e) {
            if ($e->getCode() === 403) {
                throw new Exception("Permission to `{$owner}/{$repo}` denied to {$this->user->name}.\nPlease make sure you have the correct access rights. visit https://github.com/settings/connections/applications/{$this->app->client_id}");
            }
            throw $e;
        }

        $permissions = $data['permissions'];

        if (!Arr::get($permissions, 'push')) {
            throw new AuthorizationException('权限不足');
        }

        $book = Book::get($this->app->type, [
            'id'      => $data['id'],
            'name'    => $data['name'],
            'url'     => $data['html_url'],
            'git_url' => $data['clone_url'],
            'ssh_url' => $data['ssh_url'],
        ]);

        $accessLevel = match (true) {
            Arr::get($permissions, 'admin', false) => BookMember::OWNER,
            Arr::get($permissions, 'push', false) => BookMember::MAINTAINER,
            default => BookMember::READER,
        };

        $book->members()->attach($this->user, ['access_level' => $accessLevel]);

        return $book;
    }

    public function checkPublicKey(KeyPair $keyPair)
    {
        $this->client->me()->keys()->show($keyPair->key_id);
    }

    public function createPublicKey($publicKey)
    {
        $res = $this->client->me()->keys()->create([
            'title'     => 'TopWrite',
            'key'       => $publicKey,
            'read_only' => false,
        ]);

        return $res['id'];
    }

    public function release(Release $release, Book $book, $type, File $file)
    {
        if ($type === 'html') {
            $this->pushToPages($release, $book, $type, $file);
        }
    }

    protected function makeClient()
    {
        $config = [];

        $proxy = env('PROXY');
        if ($this->app->proxy && $proxy) {
            $config['proxy'] = $proxy;
        }

        $httpClient = new \GuzzleHttp\Client($config);
        $builder    = new Builder($httpClient);

        $client = new Client($builder);
        $client->authenticate($this->user->token, null, Client::AUTH_ACCESS_TOKEN);
        return $client;
    }
}
