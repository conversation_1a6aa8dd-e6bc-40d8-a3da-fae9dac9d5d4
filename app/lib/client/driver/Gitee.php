<?php

namespace app\lib\client\driver;

use app\lib\client\ClientProxy;
use app\lib\client\Driver;
use app\lib\client\InteractsWithSocial;
use app\lib\client\PushToPages;
use app\model\Book;
use app\model\BookMember;
use app\model\KeyPair;
use app\model\Release;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use think\File;
use think\helper\Arr;
use yunwuxin\auth\exception\AuthorizationException;

class Gitee extends Driver
{
    use InteractsWithSocial, PushToPages;

    public function getBookByUrl($url)
    {
        [$owner, $repo] = $this->parseUrl($url);

        $data = $this->client->get("repos/{$owner}/{$repo}");

        $permissions = $data['permission'];

        if (!Arr::get($permissions, 'push')) {
            throw new AuthorizationException('权限不足');
        }

        $book = Book::get($this->app->type, [
            'id'      => $data['id'],
            'name'    => $data['human_name'],
            'url'     => $data['html_url'],
            'git_url' => $data['html_url'],
            'ssh_url' => $data['ssh_url'],
        ]);

        $accessLevel = match (true) {
            Arr::get($permissions, 'admin', false) => BookMember::OWNER,
            Arr::get($permissions, 'push', false) => BookMember::MAINTAINER,
            default => BookMember::READER,
        };

        $book->members()->attach($this->user, ['access_level' => $accessLevel]);

        return $book;
    }

    public function checkPublicKey(KeyPair $keyPair)
    {
        $this->client->get("user/keys/{$keyPair->key_id}");
    }

    public function createPublicKey($publicKey)
    {
        $res = $this->client->post('user/keys', [
            'form_params' => [
                'key'   => $publicKey,
                'title' => 'TopWrite',
            ],
        ]);
        return $res['id'];
    }

    public function release(Release $release, Book $book, $type, File $file)
    {
        if ($type === 'html') {
            $this->pushToPages($release, $book, $type, $file);
        }
    }

    protected function makeClient()
    {
        $stack = HandlerStack::create();
        $stack->push(Middleware::mapRequest(function (Request $request) {
            $uri = $request->getUri();

            parse_str($uri->getQuery(), $query);
            $query = array_merge($query, ['access_token' => $this->user->token]);

            return $request->withUri(
                $uri->withQuery(http_build_query($query))
            );
        }), 'auth');

        $client = new Client([
            'handler'  => $stack,
            'base_uri' => "{$this->app->url}/api/v5/",
        ]);

        return new ClientProxy($client);
    }
}
