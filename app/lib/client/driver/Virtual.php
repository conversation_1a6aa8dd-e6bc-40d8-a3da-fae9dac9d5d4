<?php

namespace app\lib\client\driver;

use app\lib\client\Driver;
use app\model\Book;
use app\model\KeyPair;
use app\model\Release;
use think\File;

class Virtual extends Driver
{

    public function getSshCommand(Book $book)
    {

    }

    public function checkPublicKey(KeyPair $keyPair)
    {

    }

    public function createPublicKey($publicKey)
    {
        return '';
    }

    public function release(Release $release, Book $book, $type, File $file)
    {

    }

    protected function makeClient()
    {

    }
}
