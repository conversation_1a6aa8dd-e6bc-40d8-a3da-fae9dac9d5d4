<?php

namespace app\lib\client\driver;

use app\lib\client\Driver;
use app\lib\client\InteractsWithSocial;
use app\lib\client\PushToPages;
use app\model\Book;
use app\model\BookMember;
use app\model\KeyPair;
use app\model\Release;
use Gitlab\Client;
use think\File;
use think\helper\Arr;
use yunwuxin\auth\exception\AuthorizationException;

/**
 * Class Gitlab
 * @package app\lib\client
 * @property Client $client
 */
class Gitlab extends Driver
{
    use InteractsWithSocial, PushToPages;

    public function getBookByUrl($url)
    {
        [$owner, $repo] = $this->parseUrl($url);

        $data = $this->client->projects()->show("{$owner}/{$repo}");

        $this->checkPermission($data);

        $book = Book::get($this->app->type, [
            'id'      => $data['id'],
            'name'    => $data['name'],
            'url'     => $data['web_url'],
            'git_url' => $data['http_url_to_repo'],
            'ssh_url' => $data['ssh_url_to_repo'],
        ]);

        $book->members()->attach($this->user, ['access_level' => BookMember::OWNER]);

        return $book;
    }

    protected function checkPermission($data)
    {
        if (
            Arr::get($data, 'namespace.kind') === 'user' &&
            Arr::get($data, 'namespace.id') === $this->user->openid
        ) {
            return true;
        }
        if (
            Arr::get($data, 'permissions.project_access') &&
            Arr::get($data, 'permissions.project_access.access_level', 0) > 0
        ) {
            return true;
        }

        if (
            Arr::get($data, 'permissions.group_access') &&
            Arr::get($data, 'permissions.group_access.access_level', 0) > 0
        ) {
            return true;
        }

        throw new AuthorizationException('权限不足');
    }

    public function checkPublicKey(KeyPair $keyPair)
    {
        $this->client->users()->key($keyPair->key_id);
    }

    public function createPublicKey($publicKey)
    {
        $res = $this->client->users()->createKey(
            'TopWrite',
            $publicKey
        );
        return $res['id'];
    }

    public function release(Release $release, Book $book, $type, File $file)
    {
        if ($type === 'html') {
            $this->pushToPages($release, $book, $type, $file, [
                '.gitlab-ci.yml' => <<<EOT
pages:
  stage: deploy
  script:
    - mkdir .public
    - cp -r * .public
    - mv .public public
  artifacts:
    paths:
      - public
  only:
    - gh-pages
EOT,
            ]);
        }
    }

    public function getReleaseTimeout(Book $book)
    {
        if (str_starts_with($this->app->url, 'https://gitlab.com')) {
            return parent::getReleaseTimeout($book);
        }
        return 60 * 20;
    }

    protected function makeClient()
    {
        $client = new Client();
        $client->setUrl($this->app->url);
        $client->authenticate($this->user->token, Client::AUTH_OAUTH_TOKEN);

        return $client;
    }
}
