<?php

namespace app\lib\client;

use Exception;
use think\exception\ValidateException;
use think\helper\Str;

trait InteractsWithSocial
{
    protected function parseUrl($url)
    {
        if (!Str::startsWith($url, $this->app->url)) {
            throw new ValidateException('url invalid');
        }
        try {
            $parsed = parse_url($url);
            ['dirname' => $dirname, 'filename' => $filename] = pathinfo($parsed['path']);

            return explode('/', trim($dirname . '/' . $filename, '/'), 2);
        } catch (Exception) {
            throw new ValidateException('url invalid');
        }
    }
}
