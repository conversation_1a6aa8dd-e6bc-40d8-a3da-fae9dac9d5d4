<?php

namespace app\lib\client;

use app\model\App;
use app\model\Book;
use app\model\KeyPair;
use app\model\Release;
use app\model\User;
use Exception;
use GuzzleHttp\Client;
use RuntimeException;
use think\File;

/**
 * Class Driver
 * @package app\lib\client
 * @property Client $client
 */
abstract class Driver
{
    protected $client;

    public function __construct(protected App $app, protected User $user)
    {
        $this->client = $this->makeClient();
    }

    /**
     * @return Book
     */
    public function getBookByUrl($url)
    {
        throw new RuntimeException('Not Support');
    }

    abstract public function checkPublicKey(KeyPair $keyPair);

    /**
     * @param $publicKey
     * @return string
     */
    abstract public function createPublicKey($publicKey);

    abstract public function release(Release $release, Book $book, $type, File $file);

    public function getRemotePath(Book $book)
    {
        return $book->ssh_url ?: $book->git_url;
    }

    public function getSshCommand(Book $book)
    {
        $keyPair = KeyPair::getOrNew($this->user,
            function (KeyPair $keyPair) {
                if (empty($keyPair->key_id)) {
                    return false;
                }
                try {
                    $this->checkPublicKey($keyPair);
                    return true;
                } catch (Exception) {
                    return false;
                }
            },
            function (KeyPair $keyPair) {
                $id = $this->createPublicKey($keyPair->public_key);
                $keyPair->save(['key_id' => $id]);
            }
        );

        //TODO 缓存key
        $keyFile = "/var/www/.ssh/keys/key_{$keyPair->id}";

        file_put_contents($keyFile, $keyPair->private_key);
        chmod($keyFile, 0600);

        $command = "ssh -i {$keyFile}";

        $proxy = env('PROXY');
        if ($this->app->proxy && $proxy) {
            $proxy   = parse_url($proxy);
            $command .= " -oProxyCommand=\"corkscrew {$proxy['host']} {$proxy['port']} %h %p\"";
        }

        return $command;
    }

    public function getPresetPlugins(Book $book)
    {
        return [];
    }

    public function getPoweredBy(Book $book)
    {
        return [
            'name' => '顶想云',
            'link' => 'https://www.topthink.com',
        ];
    }

    public function getAssetType(Book $book)
    {
        return 'local';
    }

    public function getReleaseTypes(Book $book)
    {
        return ['html'];
    }

    public function getReleaseTimeout(Book $book)
    {
        return 300;
    }

    public function getLfsFiles(Book $book)
    {
        return [];
    }

    abstract protected function makeClient();

}
