<?php

namespace app\lib;

use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use app\repo\tools\Create;
use app\repo\tools\Insert;
use app\repo\tools\StrReplace;
use app\repo\tools\View;
use app\repo\Workspace;
use think\ai\Client;
use think\helper\Arr;

class Agent extends \think\agent\Agent
{

    /** @var Conversation */
    protected $conversation;

    protected $isNewConversation = false;

    /** @var Message */
    protected $message;

    public function __construct(protected Space $space, protected Workspace $workspace)
    {
    }

    protected function initConversation($params)
    {
        $input          = Arr::get($params, 'input');
        $conversationId = Arr::get($params, 'conversation');

        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $conversation = $this->space->conversations()->find($conversationId);
            }
        }

        if (empty($conversation)) {
            $conversation = $this->space->conversations()->save([]);

            $this->isNewConversation = true;
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;

        $this->message = $this->conversation->messages()->make([
            'space_id' => $this->space->id,
            'input'    => $input,
        ]);
    }

    protected function initTools()
    {
        //文本编辑工具
        $this->addFunction('view', new View($this->workspace));
        $this->addFunction('str_replace', new StrReplace($this->workspace));
        $this->addFunction('create', new Create($this->workspace));
        $this->addFunction('insert', new Insert($this->workspace));

        //内置插件
        $this->addPlugin('xkazpYdJ', 'metasoSearchWeb');//网页搜索
        $this->addPlugin('xkazpYdJ', 'metasoSearchPic');//图片搜索
        $this->addPlugin('xkazpYdJ', 'metasoRead');//读取网页内容
        $this->addPlugin('l9av2maG', 'jimeng');//即梦绘图
    }

    protected function buildPromptMessages()
    {
        // TODO: Implement buildPromptMessages() method.
    }

    protected function init($params)
    {
        $this->initConversation($params);
        $this->initTools();
    }

    protected function saveMessage($usage, $latency)
    {
        // TODO: Implement saveMessage() method.
    }

    protected function consumeTokens(int $usage): int
    {
        return $usage;
    }

    protected function getClient(): Client
    {
        // TODO: Implement getClient() method.
    }
}
