<?php

namespace app\middleware;

use Closure;
use yunwuxin\Auth;

class Authentication
{
    protected $auth;

    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    public function handle($request, Closure $next)
    {
        $user = $this->auth->user();

        if ($user) {
            return $next($request);
        }

        return redirect('/auth/login')->remember();
    }
}
