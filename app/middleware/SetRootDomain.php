<?php

namespace app\middleware;

use Closure;
use think\Config;
use think\Request;

class SetRootDomain
{

    public function __construct(protected Config $config)
    {
    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $host = parse_url($this->config->get('app.host'), PHP_URL_HOST);

        $request->setRootDomain($host);

        return $next($request);
    }
}
