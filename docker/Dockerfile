FROM registry.cn-shanghai.aliyuncs.com/topthink/php:8.2-swoole-nginx

#安装git
RUN \
    sed -i "s/archive.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list && \
    curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.deb.sh | bash

RUN \
    apt-get update && \
    apt-get -y --no-install-recommends install \
    git git-lfs=3.4.0 corkscrew openssh-client rsync

COPY docker/ripgrep_13.0.0_amd64.deb /tmp/
RUN dpkg -i /tmp/ripgrep_13.0.0_amd64.deb
RUN rm /tmp/ripgrep_13.0.0_amd64.deb

RUN apt-get clean && rm -rf /var/cache/apt/* && rm -rf /var/lib/apt/lists/*

RUN \
    pecl install inotify && \
    echo "extension=inotify.so" > /etc/php/8.2/mods-available/inotify.ini && \
    phpenmod inotify
