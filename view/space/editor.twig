<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{{ "TopWrite Editor"|lang }}</title>
</head>
<body>
<div id="root"></div>
<script type='text/javascript'>
    history.replaceState(null, "", location.href);
</script>
<script src="https://jsdelivr.topthink.com/npm/lodash@4.17/lodash.min.js"></script>
<script src="https://jsdelivr.topthink.com/npm/react@18/umd/react.production.min.js"></script>
<script src="https://jsdelivr.topthink.com/npm/react-dom@18/umd/react-dom.production.min.js"></script>
<script src="https://jsdelivr.topthink.com/npm/socket.io-client@4/dist/socket.io.min.js"></script>
<script src="https://jsdelivr.topthink.com/npm/@topwrite/editor@{{ version }}/dist/index.js"></script>
</body>
</html>
