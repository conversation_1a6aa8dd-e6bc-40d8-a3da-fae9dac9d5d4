{% extends "layout.twig" %}
{% block main %}
    <div class="container mt-4">
        <h3>{{ "Books"|lang }}</h3>
        <p>Manage recent books.</p>
        <hr />
        <form method="get" action="/book/edit" target="_blank">
            <div class="input-group">
                <input type="url" class="form-control" required name="url" placeholder="{{ placeholder }}" />
                <button class="btn btn-primary" type="submit">{{ "Open Book"|lang }}</button>
            </div>
        </form>
        {% if books is empty %}
            <div class="jumbotron jumbotron-fluid text-center bg-white mt-3">
                <div class="container">
                    <h1 class="display-4">{{ "No Books"|lang }}</h1>
                    <p class="lead">{{ "Create a new book for a recently used books. Learn more"|lang }}</p>
                </div>
            </div>
        {% else %}
            <table class="table table-hover align-middle mt-3">
                <thead>
                <tr>
                    <th>Name</th>
                    <th>Url</th>
                    <th>Update Time</th>
                    <th class='text-end'>Actions</th>
                </tr>
                </thead>
                <tbody>
                {% for book in books %}
                    <tr>
                        <td>
                            <a href="/book/edit?url={{ book.url }}" target="_blank">
                                {{ book.name }}
                            </a>
                        </td>
                        <td class="text-black-50">
                            <a href="{{ book.url }}" target="_blank">{{ book.url }}</a>
                        </td>
                        <td class="text-black-50">{{ book.update_time.diffForHumans() }}</td>
                        <td class="text-end">
                            <div class="dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-end shadow-sm">
                                    <a class="dropdown-item" href="/book/edit?url={{ book.url }}"
                                       target="_blank">{{ "Edit"|lang }}</a>
                                    <a class="dropdown-item" href="/book/{{ book.id }}"
                                       data-bs-api
                                       data-confirm='{{ "Are you sure you want to delete it?"|lang }}'
                                       data-method='delete'>{{ "Delete"|lang }}</a>
                                </div>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
            {{ books.render()|raw }}
        {% endif %}
    </div>
{% endblock %}
