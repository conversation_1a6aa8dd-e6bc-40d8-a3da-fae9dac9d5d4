FROM registry.cn-shanghai.aliyuncs.com/topthink/base:write-x

ENV GIT_EDITOR=true

# 配置git
RUN git config --system core.filemode false
RUN git config --system core.quotepath false
RUN git config --system diff.renames false
RUN git config --system user.email <EMAIL>
RUN git config --system user.name TopWrite
# lfs
RUN git config --system lfs.storage /opt/repo/lfs
RUN git config --system lfs.skipdownloaderrors true
RUN git config --system lfs.forceprogress true
RUN git config --system lfs.customtransfer.topthink.path php
RUN git config --system lfs.customtransfer.topthink.args "/opt/htdocs/think git:lfs"
RUN git config --system lfs.customtransfer.topthink.concurrent false
RUN git config --system lfs.customtransfer.topthink.direction upload

COPY docker/ssh /var/www/.ssh
RUN mkdir /var/www/.ssh/hosts /var/www/.ssh/keys
RUN chown www-data:www-data -R /var/www/.ssh
RUN chmod 600 /var/www/.ssh/config

#配置supervisor
ADD docker/supervisord.conf /etc/supervisor/conf.d/web.conf

COPY docker/entrypoint.sh /sbin/entrypoint.sh
RUN chmod 755 /sbin/entrypoint.sh

#配置nginx
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

#安装代码
COPY . /opt/htdocs

RUN mkdir /opt/repo

RUN chown www-data:www-data -R /opt/htdocs/runtime /opt/repo

ENTRYPOINT ["/sbin/entrypoint.sh"]
CMD ["app:start"]
